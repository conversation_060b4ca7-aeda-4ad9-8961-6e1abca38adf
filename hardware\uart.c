#include "main.h"

unsigned char uart0_recv_buf[5] = {0};
unsigned char uart0_send_buf[5] = {0};
unsigned char recv_count = 0;
unsigned char recv_flag = 0;
unsigned char packet_len = 5;

uint8_t ble_pair_flag = 0;
uint8_t ble_reconnect_flag = 0;

void uart_init()
{
    SET_REG(PORTIDX, PORT4);
    SET_REG_BITS(PINMOD54, PINMOD5, PIN_MODE_PP);
    SET_REG_BITS(PINMOD54, PINMOD4, PIN_MODE_OD_IPU);
    P4_4 = 1;
    P4_5 = 1;

    SET_REG_BITS(PINMODE, UART0PS, 2);    // UART0脚位选择P44 P45
    SET_REG_BITS(PCON, SMOD, SMOD_PRICE); // 双波特率控制位

    SM0 = 0;
    SM1 = 1; // 模式1
    SM2 = 0;

    SET_REG_BITS(UART0CON, UART0BRS, 1);
    SET_REG_BITS(UART0CON, UART0BRP, UART0BRP);

    TI = 0;  // 先清发送中断 标志
    RI = 0;  // 先清接收中断 标志
    REN = 1; // UART接收收据使能
    ES = 1;
}

void uart0_send(unsigned char *buf, unsigned char len)
{
    unsigned char buffer[5] = {0};
    unsigned char check_sum = 0;
    unsigned char i = 0;
    // 帧头
    buffer[0] = 0x20;
    // 数据
    for (i = 0; i < len - 2; i++)
    {
        buffer[1 + i] = buf[i];
    }
    // 校验
    for (i = 0; i < len - 1; i++)
    {
        check_sum += buffer[i];
    }
    buffer[len - 1] = check_sum;

    for (i = 0; i < len; i++)
    {
        SBUF = buffer[i];
        while (TI == 0)
            ;
        TI = 0;
    }
}

void uart_data_handle(void)
{
    if (time_state == STATE_POWER_OFF)
        return;

    if (recv_flag)
    {
        recv_flag = 0;
        switch (uart0_recv_buf[1])
        {
        case 0x04:                         // 回连状态
            if (uart0_recv_buf[2] == 0x01) // 蓝牙连接成功
            {
                ble_pair_flag = 0;
                ble_reconnect_flag = 0;
                music_mode = MODE_BT;

                if (time_state == STATE_BT_NUM)
                {
                    time_state = STATE_REAL_TIME;
                }

                // 发送蓝牙连接成功消息
                uart0_send_buf[0] = 0x04;
                uart0_send_buf[1] = 0x01;
                uart0_send(uart0_send_buf, 4);
                uart0_send_buf[0] = 0x00;
                uart0_send_buf[1] = 0x00;
            }
            else if (uart0_recv_buf[2] == 0x02) // 蓝牙断开
            {
                if (music_mode == MODE_BT)
                {
                    ble_pair_flag = 1;
                    ble_reconnect_flag = 0;

                    music_mode = MODE_BT_PAIR;
                    time_state = STATE_BT_NUM;
                    // 设置LED_WHITE呼吸效果的最大亮度为当前环境灯亮度
                    set_white_duty_config_by_ambient_light();
                }
            }
            else if (uart0_recv_buf[2] == 0x04) // aux进入
            {
                music_mode = MODE_AUX;
            }
            else if (uart0_recv_buf[2] == 0x03) // aux进入失败
            {
                music_mode = MODE_IDE;
            }
            break;
        case 0x02: // 音量
            if (music_mode == MODE_IDE)
            {
                vol.idl_vol = uart0_recv_buf[2];
            }
            else if (music_mode == MODE_BT)
            {
                vol.bt_vol = uart0_recv_buf[2];
            }
            else if (music_mode == MODE_AUX)
            {
                vol.aux_vol = uart0_recv_buf[2];
            }
            else if (music_mode == MODE_AMBIENT)
            {
                vol.ambient_vol = uart0_recv_buf[2];
            }
            break;
        case 0x03: // 时间
            rtc.hour = uart0_recv_buf[2];
            rtc.minute = uart0_recv_buf[3];
            break;
        case 0x05: // 蓝牙名称
            bt_name_num_thousand = uart0_recv_buf[2] / 10;
            bt_name_num_hundred = uart0_recv_buf[2] % 10;
            bt_name_num_ten = uart0_recv_buf[3] / 10;
            bt_name_num_one = uart0_recv_buf[3] % 10;
            break;
        }

        uart0_recv_buf[0] = 0;
        uart0_recv_buf[1] = 0;
        uart0_recv_buf[2] = 0;
        uart0_recv_buf[3] = 0;
        uart0_recv_buf[4] = 0;
    }
}

void uart0_irq() interrupt 4
{
    unsigned char checksum = 0;
    unsigned char i = 0;
    if (RI)
    {
        uart0_recv_buf[recv_count] = SBUF; // 读取数据
        RI = 0;                            // 清除接收标志

        if (recv_count == 0)
        {
            if (uart0_recv_buf[0] == 0x20) // 0x20为帧头
            {
                packet_len = 5;
                recv_count++;
            }
        }
        else if (recv_count == 1)
        {
            if (uart0_recv_buf[1] == 0x02 || uart0_recv_buf[1] == 0x04)
            {
                packet_len = 4;
            }
            recv_count++;
        }
        else
        {
            recv_count++;
        }

        if (recv_count >= packet_len)
        {
            recv_count = 0;
            for (i = 0; i < packet_len - 1; i++)
            {
                checksum += uart0_recv_buf[i];
            }

            if (checksum == uart0_recv_buf[packet_len - 1])
            {
                recv_flag = 1; // 接收成功
            }
        }
    }
}