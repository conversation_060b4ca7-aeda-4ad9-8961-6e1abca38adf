C51 COMPILER V9.60.7.0   FLASH                                                             07/26/2025 11:36:03 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE FLASH
OBJECT MODULE PLACED IN .\Objects\flash.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\flash.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hardwa
                    -re) DEBUG OBJECTEXTEND PRINT(.\Listings\flash.lst) TABS(2) OBJECT(.\Objects\flash.obj)

line level    source

   1          #include "main.h"
   2          
   3          uint8_t scan_flash_time = 0;
   4          uint8_t ble_blink_cnt = 0;
   5          uint8_t timer_flash_cnt = 0; // 定时器闪灯
   6          
   7          // 蓝牙配对闪烁
   8          void ble_flash()
   9          {
  10   1          if (ble_pair_flag)
  11   1          {
  12   2              ble_blink_cnt++;
  13   2              if (ble_blink_cnt >= 40)
  14   2              {
  15   3                  ble_blink_cnt = 0;
  16   3              }
  17   2              if (ble_blink_cnt >= 20) // 200ms
  18   2              {
  19   3                  KEY_LED_BT = 1;
  20   3              }
  21   2              else
  22   2              {
  23   3                  KEY_LED_BT = 0;
  24   3              }
  25   2          }
  26   1          else
  27   1          {
  28   2              ble_blink_cnt = 0;
  29   2          }
  30   1      }
  31          
  32          // 设置定时器闪烁
  33          void set_timer_flash()
  34          {
  35   1          if (time_state == STATE_SET_COUNT_DOWN)
  36   1          {
  37   2              timer_flash_cnt++;
  38   2              if (timer_flash_cnt >= 40) // 400ms
  39   2              {
  40   3                  timer_flash_cnt = 0;
  41   3              }
  42   2              if (timer_flash_cnt >= 20) // 200ms
  43   2              {
  44   3                  KEY_LED_TIMER = 0;
  45   3              }
  46   2              else
  47   2              {
  48   3                  KEY_LED_TIMER = 1;
  49   3              }
  50   2          }
  51   1          else
  52   1          {
  53   2              timer_flash_cnt = 0;
  54   2          }
C51 COMPILER V9.60.7.0   FLASH                                                             07/26/2025 11:36:03 PAGE 2   

  55   1      }
  56          
  57          void led_flash_task(void)
  58          {
  59   1          if (time_state == STATE_POWER_OFF)
  60   1              return;
  61   1          if (scan_flash_time > 10)
  62   1          {
  63   2              scan_flash_time = 0;
  64   2      #if BT_FLASH_ENABLE
                      ble_flash();
              #endif
  67   2              set_timer_flash();
  68   2          }
  69   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    104    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =      3    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
