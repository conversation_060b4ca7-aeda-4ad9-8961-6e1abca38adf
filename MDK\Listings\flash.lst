C51 COMPILER V9.60.7.0   FLASH                                                             08/01/2025 16:44:48 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE FLASH
OBJECT MODULE PLACED IN .\Objects\flash.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\flash.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hardwa
                    -re) DEBUG OBJECTEXTEND PRINT(.\Listings\flash.lst) TABS(2) OBJECT(.\Objects\flash.obj)

line level    source

   1          #include "main.h"
   2          
   3          uint8_t scan_flash_time = 0;
   4          uint8_t ble_blink_cnt = 0;
   5          uint8_t timer_flash_cnt = 0; // 定时器闪灯
   6          uint8_t aux_flash_cnt = 0;   // AMB灯闪烁计数器（用于AUX按键反馈）
   7          uint8_t aux_flash_flag = 0;  // AMB灯闪烁标志（用于AUX按键反馈）
   8          
   9          // 蓝牙配对闪烁
  10          void ble_flash()
  11          {
  12   1          if (ble_pair_flag)
  13   1          {
  14   2              ble_blink_cnt++;
  15   2              if (ble_blink_cnt >= 40)
  16   2              {
  17   3                  ble_blink_cnt = 0;
  18   3              }
  19   2              if (ble_blink_cnt >= 20) // 200ms
  20   2              {
  21   3                  KEY_LED_BT = 1;
  22   3              }
  23   2              else
  24   2              {
  25   3                  KEY_LED_BT = 0;
  26   3              }
  27   2          }
  28   1          else
  29   1          {
  30   2              ble_blink_cnt = 0;
  31   2          }
  32   1      }
  33          
  34          // 设置定时器闪烁
  35          void set_timer_flash()
  36          {
  37   1          if (time_state == STATE_SET_COUNT_DOWN)
  38   1          {
  39   2              timer_flash_cnt++;
  40   2              if (timer_flash_cnt >= 40) // 400ms
  41   2              {
  42   3                  timer_flash_cnt = 0;
  43   3              }
  44   2              if (timer_flash_cnt >= 20) // 200ms
  45   2              {
  46   3                  KEY_LED_TIMER = 0;
  47   3              }
  48   2              else
  49   2              {
  50   3                  KEY_LED_TIMER = 1;
  51   3              }
  52   2          }
  53   1          else
  54   1          {
C51 COMPILER V9.60.7.0   FLASH                                                             08/01/2025 16:44:48 PAGE 2   

  55   2              timer_flash_cnt = 0;
  56   2          }
  57   1      }
  58          
  59          // AMB灯闪烁（用于指示按键音屏蔽状态切换）
  60          void aux_flash()
  61          {
  62   1          if (aux_flash_flag)
  63   1          {
  64   2              aux_flash_cnt++;
  65   2              if (aux_flash_cnt >= 100) // 200ms闪烁一次后停止
  66   2              {
  67   3                  aux_flash_cnt = 0;
  68   3                  aux_flash_flag = 0;
  69   3                  KEY_LED_AMB = 0; // 恢复常亮状态
  70   3              }
  71   2              else if (aux_flash_cnt >= 50) // 100ms
  72   2              {
  73   3                  KEY_LED_AMB = 1; // 熄灭
  74   3              }
  75   2              else
  76   2              {
  77   3                  KEY_LED_AMB = 0; // 点亮
  78   3              }
  79   2          }
  80   1      }
  81          
  82          void led_flash_task(void)
  83          {
  84   1          if (time_state == STATE_POWER_OFF)
  85   1              return;
  86   1          if (scan_flash_time > 10)
  87   1          {
  88   2              scan_flash_time = 0;
  89   2      #if BT_FLASH_ENABLE
                      ble_flash();
              #endif
  92   2              set_timer_flash();
  93   2              aux_flash();
  94   2          }
  95   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    147    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =      5    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
