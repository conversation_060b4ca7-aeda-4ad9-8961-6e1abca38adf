#include "TM52F1386_bsp.h"
#include <REGtenxTM52eF1385.h>
#include <intrins.h>

/**********************************************************************************************************
**函数名称  bsp_clock_init()
**函数描述 ：设置内部FRC/2为系统时钟
**输    入 ：无
**输    出 ：无
**说    明 ：快时钟系统主频为 18.432/2M   默认二分频
**********************************************************************************************************/
void bsp_clock_init()
{
    SELFCK = 0; // 切换到慢时钟

    // CLKCON = 0x02; // 时钟二分频
    CLKCON = 0x03; // 时钟一分频

    STPPCK = 0; // 开启部分模块使用快时钟
    STPFCK = 0; // 开启快时钟
    SELFCK = 1; // 切换到快时钟
}
