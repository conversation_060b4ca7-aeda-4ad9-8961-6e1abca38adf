IC:TM52eF1385
	<Option>
	  <Name>PROT</Name>
	  <Location>0x80</Location>
	  <StartBit>7</StartBit>
	  <Length>1</Length>
	  <Option Value="1">Disable</Option>
	  <Option Value="0">Enable</Option>
	</Option>

	<Option>
	  <Name>XRSTE</Name>
	  <Location>0x40</Location>
	  <StartBit>6</StartBit>
	  <Length>1</Length>
	  <Option Value="1">Disable</Option>
	  <Option Value="0">Enable</Option>
	</Option>

	<Option>
	  <Name>BOOTV</Name>
	  <Location>0x03</Location>
	  <StartBit>0</StartBit>
	  <Length>2</Length>
	  <Option Value="0">0x0000</Option>
	  <Option Value="1">0x0000</Option>
	  <Option Value="2">0xE000(BOOT area 7K)</Option>
	  <Option Value="3">0xE800(BOOT area 5K)</Option>
	</Option>

	<Option>
	  <Name>IAP data reserve range</Name>
	  <Location>0x0F</Location>
	  <StartBit>0</StartBit>
	  <Length>4</Length>
	  <Option Value="15">No reserve range</Option>
	  <Option Value="14">512 bytes(DE00~DFFF)</Option>
	  <Option Value="13">1024 bytes(DC00~DFFF)</Option>
	</Option>

	<Option>
	  <Name>ICE Mode</Name>
	  <Location>0x10</Location>
	  <StartBit>4</StartBit>
	  <Length>1</Length>
	  <Option Value="1">4-Wire</Option>
	  <Option Value="0">5-Wire</Option>
	</Option>

	<Option>
	  <Name>On Chip CRC16</Name>
	  <Location>0x20</Location>
	  <StartBit>5</StartBit>
	  <Length>1</Length>
	  <Option Value="0">Disable</Option>
	  <Option Value="1">Enable</Option>
	</Option>

