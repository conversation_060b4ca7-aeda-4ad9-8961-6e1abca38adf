C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE PWM
OBJECT MODULE PLACED IN .\Objects\pwm.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\pwm.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hardware
                    -) DEBUG OBJECTEXTEND PRINT(.\Listings\pwm.lst) TABS(2) OBJECT(.\Objects\pwm.obj)

line level    source

   1          #include "main.h"
   2          
   3          #define BREATH_CNT_MAX          100
   4          #define BREATH_FADE_MAX         1
   5          
   6          // 显示LED呼吸效果专用参数 - 使用和按键LED相同的参数
   7          #define DISPLAY_BREATH_CNT_MAX  100 // 和按键LED相同的PWM周期
   8          #define DISPLAY_BREATH_FADE_MAX 1   // 和按键LED相同的渐变速度
   9          #define DISPLAY_BREATH_DUTY_MAX 100 // PWM占空比范围0-100
  10          
  11          uint8_t breath_bt_cnt = 0;
  12          uint8_t breath_bt_dir = 0;
  13          uint8_t breath_bt_fade = 0;
  14          uint8_t breath_bt_duty = 0;
  15          
  16          #if TIMER_BREATH_ENABLE
              uint8_t breath_timer_cnt = 0;
              uint8_t breath_timer_dir = 0;
              uint8_t breath_timer_fade = 0;
              uint8_t breath_timer_duty = 0;
              #endif
  22          
  23          #if AUX_BREATH_ENABLE
              uint8_t breath_aux_cnt = 0;
              uint8_t breath_aux_dir = 0;
              uint8_t breath_aux_fade = 0;
              uint8_t breath_aux_duty = 0;
              #endif
  29          
  30          #if SNOOZE_BREATH_ENABLE
  31          uint8_t breath_snooze_cnt = 0;
  32          uint8_t breath_snooze_dir = 0;
  33          uint8_t breath_snooze_fade = 0;
  34          uint8_t breath_snooze_duty = 0;
  35          #endif
  36          
  37          #if MUSIC_BREATH_ENABLE
              uint8_t breath_music_cnt = 0;
              uint8_t breath_music_dir = 0;
              uint8_t breath_music_fade = 0;
              uint8_t breath_music_duty = 0;
              #endif
  43          
  44          uint8_t breath_white_cnt = 0;
  45          uint8_t breath_white_dir = 0;
  46          uint8_t breath_white_fade = 0;
  47          uint8_t breath_white_duty = 0;
  48          
  49          uint8_t white_duty_config = 0;
  50          
  51          // 根据当前环境灯亮度设置white_duty_config
  52          void set_white_duty_config_by_ambient_light(void)
  53          {
  54   1          switch (ambient_light)
C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 2   

  55   1          {
  56   2          case KEY_LED_LEVEL_0:
  57   2              white_duty_config = 0;
  58   2              break;
  59   2          case KEY_LED_LEVEL_1:
  60   2              white_duty_config = 25;
  61   2              break;
  62   2          case KEY_LED_LEVEL_2:
  63   2              white_duty_config = 50;
  64   2              break;
  65   2          case KEY_LED_LEVEL_3:
  66   2              white_duty_config = 75;
  67   2              break;
  68   2          case KEY_LED_LEVEL_4:
  69   2              white_duty_config = 100;
  70   2              break;
  71   2          default:
  72   2              white_duty_config = 100; // 默认最大亮度
  73   2              break;
  74   2          }
  75   1      }
  76          
  77          uint8_t pwm_led_on_flag = 0;  // 渐亮标志位
  78          uint8_t pwm_led_off_flag = 0; // 渐灭标志位
  79          
  80          uint8_t breath_power_cnt = 0;
  81          uint8_t breath_power_fade = 0;
  82          uint8_t breath_power_duty = 0;
  83          
  84          uint8_t breath_off_cnt = 0;
  85          uint8_t breath_off_fade = 0;
  86          uint8_t breath_off_duty = 100;
  87          
  88          // 显示时间LED呼吸效果控制变量
  89          uint8_t display_led_breath_on_flag = 0;  // 显示LED渐亮标志位
  90          uint8_t display_led_breath_off_flag = 0; // 显示LED渐灭标志位
  91          
  92          uint8_t breath_display_on_cnt = 0;
  93          uint8_t breath_display_on_fade = 0;
  94          uint8_t breath_display_on_duty = 0;
  95          
  96          uint8_t breath_display_off_cnt = 0;
  97          uint8_t breath_display_off_fade = 0;
  98          uint8_t breath_display_off_duty = 100;
  99          
 100          void key_all_led(uint8_t state) // 1 关闭 0 开启
 101          {
 102   1          KEY_LED_POWER = state;
 103   1          KEY_LED_VOLUME_DOWN = state;
 104   1          KEY_LED_VOLUME_UP = state;
 105   1          KEY_LED_AUX = state;
 106   1          KEY_LED_BT = state;
 107   1          KEY_LED_TIMER = state;
 108   1          KEY_LED_AMB = state;
 109   1          KEY_LED_MUSIC = state;
 110   1          KEY_LED_PP = state;
 111   1          // LED_SNOOZE = state;
 112   1      }
 113          
 114          void breath_led_exe(void) // 100us
 115          {
 116   1          // 显示时间LED呼吸效果处理 - 渐亮
C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 3   

 117   1          if (display_led_breath_on_flag)
 118   1          {
 119   2              breath_display_on_cnt++;
 120   2              // LED_WHITE氛围灯呼吸效果控制 - 使用和按键LED相同的PWM方式
 121   2              if (breath_display_on_cnt < breath_display_on_duty)
 122   2              {
 123   3                  LED_WHITE = 1; // 关闭氛围灯
 124   3              }
 125   2              else
 126   2              {
 127   3                  LED_WHITE = 0; // 开启氛围灯
 128   3              }
 129   2      
 130   2              if (breath_display_on_cnt >= DISPLAY_BREATH_CNT_MAX)
 131   2              {
 132   3                  breath_display_on_cnt = 0;
 133   3                  breath_display_on_fade++;
 134   3                  if (breath_display_on_fade >= DISPLAY_BREATH_FADE_MAX)
 135   3                  {
 136   4                      breath_display_on_fade = 0;
 137   4                      breath_display_on_duty++;
 138   4                      if (breath_display_on_duty >= DISPLAY_BREATH_DUTY_MAX)
 139   4                      {
 140   5                          breath_display_on_duty = DISPLAY_BREATH_DUTY_MAX;
 141   5                          display_led_breath_on_flag = 0;
 142   5                          // 渐亮完成，LED_WHITE保持开启状态
 143   5                          LED_WHITE = 0;
 144   5                      }
 145   4                  }
 146   3              }
 147   2          }
 148   1          else
 149   1          {
 150   2              breath_display_on_cnt = 0;
 151   2          }
 152   1      
 153   1          // 显示时间LED呼吸效果处理 - 渐灭
 154   1          if (display_led_breath_off_flag)
 155   1          {
 156   2              breath_display_off_cnt++;
 157   2              // LED_WHITE氛围灯呼吸效果控制
 158   2              if (breath_display_off_cnt < breath_display_off_duty)
 159   2              {
 160   3                  LED_WHITE = 1; // 关闭氛围灯
 161   3              }
 162   2              else
 163   2              {
 164   3                  LED_WHITE = 0; // 开启氛围灯
 165   3              }
 166   2      
 167   2              if (breath_display_off_cnt >= DISPLAY_BREATH_CNT_MAX)
 168   2              {
 169   3                  breath_display_off_cnt = 0;
 170   3                  breath_display_off_fade++;
 171   3                  if (breath_display_off_fade >= DISPLAY_BREATH_FADE_MAX)
 172   3                  {
 173   4                      breath_display_off_fade = 0;
 174   4                      breath_display_off_duty--;
 175   4                      if (breath_display_off_duty == 0)
 176   4                      {
 177   5                          breath_display_off_duty = 0;
 178   5                          display_led_breath_off_flag = 0;
C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 4   

 179   5                          // 渐灭完成，LED_WHITE保持关闭状态
 180   5                          LED_WHITE = 1;
 181   5                      }
 182   4                  }
 183   3              }
 184   2          }
 185   1          else
 186   1          {
 187   2              breath_display_off_cnt = 0;
 188   2          }
 189   1      
 190   1      #if 0
                  if (time_state == STATE_POWER_OFF || time_state == STATE_SLEEP)
                      return;
              #else
 194   1          if (pwm_led_on_flag)
 195   1          {
 196   2              breath_power_cnt++;
 197   2              if (breath_power_cnt < breath_power_duty)
 198   2              {
 199   3                  key_all_led(0);
 200   3              }
 201   2              else
 202   2              {
 203   3                  key_all_led(1);
 204   3              }
 205   2      
 206   2              if (breath_power_cnt >= BREATH_CNT_MAX)
 207   2              {
 208   3                  breath_power_cnt = 0;
 209   3                  breath_power_fade++;
 210   3                  if (breath_power_fade >= BREATH_FADE_MAX)
 211   3                  {
 212   4                      breath_power_fade = 0;
 213   4                      breath_power_duty++;
 214   4                      if (breath_power_duty >= 100)
 215   4                      {
 216   5                          breath_power_duty = 0;
 217   5                          key_all_led(0);
 218   5                          pwm_led_on_flag = 0;
 219   5                      }
 220   4                  }
 221   3              }
 222   2      
 223   2              return;
 224   2          }
 225   1          else
 226   1          {
 227   2              breath_power_cnt = 0;
 228   2          }
 229   1      
 230   1          if (pwm_led_off_flag)
 231   1          {
 232   2              breath_off_cnt++;
 233   2              if (breath_off_cnt < breath_off_duty)
 234   2              {
 235   3                  key_all_led(0);
 236   3              }
 237   2              else
 238   2              {
 239   3                  key_all_led(1);
 240   3              }
C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 5   

 241   2      
 242   2              if (breath_off_cnt >= BREATH_CNT_MAX)
 243   2              {
 244   3                  breath_off_cnt = 0;
 245   3                  breath_off_fade++;
 246   3                  if (breath_off_fade >= BREATH_FADE_MAX)
 247   3                  {
 248   4                      breath_off_fade = 0;
 249   4                      breath_off_duty--;
 250   4                      if (breath_off_duty == 0)
 251   4                      {
 252   5                          breath_off_duty = 100;
 253   5                          key_all_led(1);
 254   5                          LED_SNOOZE = 1;
 255   5                          pwm_led_off_flag = 0;
 256   5                      }
 257   4                  }
 258   3              }
 259   2              return;
 260   2          }
 261   1          else
 262   1          {
 263   2              breath_off_cnt = 0;
 264   2          }
 265   1      
 266   1      #endif
 267   1      
 268   1          if (time_state == STATE_POWER_OFF || time_state == STATE_SLEEP)
 269   1              return;
 270   1      
 271   1      #if BT_MODE_BREATH_ENABLE
                  if (music_mode == MODE_BT || music_mode == MODE_BT_RECONNECT)
              #endif
 274   1              if (music_mode == MODE_BT_PAIR)
 275   1              {
 276   2                  breath_bt_cnt++;
 277   2                  if (breath_bt_cnt < breath_bt_duty)
 278   2                  {
 279   3                      KEY_LED_BT = 0;
 280   3                  }
 281   2                  else
 282   2                  {
 283   3                      KEY_LED_BT = 1;
 284   3                  }
 285   2              }
 286   1              else
 287   1              {
 288   2                  breath_bt_cnt = 0;
 289   2              }
 290   1      
 291   1      #if TIMER_BREATH_ENABLE
                  if (count_down_flag && !already_countdown)
                  {
                      breath_timer_cnt++;
                      if (breath_timer_cnt < breath_timer_duty)
                      {
                          KEY_LED_TIMER = 0;
                      }
                      else
                      {
                          KEY_LED_TIMER = 1;
                      }
C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 6   

                  }
                  else
                  {
                      breath_timer_cnt = 0;
                  }
              
                  if (breath_timer_cnt >= BREATH_CNT_MAX)
                  {
                      breath_timer_cnt = 0;
                      breath_timer_fade++;
                      if (breath_timer_fade >= BREATH_FADE_MAX)
                      {
                          breath_timer_fade = 0;
                          if (breath_timer_dir)
                          {
                              breath_timer_duty--;
                              if (breath_timer_duty == 0)
                              {
                                  breath_timer_dir = 0;
                              }
                          }
                          else
                          {
                              breath_timer_duty++;
                              if (breath_timer_duty >= 100)
                              {
                                  breath_timer_duty = 100;
                                  breath_timer_dir = 1;
                              }
                          }
                      }
                  }
              
              #endif
 337   1      
 338   1      #if AUX_BREATH_ENABLE
              
                  if (music_mode == MODE_AUX)
                  {
                      breath_aux_cnt++;
                      if (breath_aux_cnt < breath_aux_duty)
                      {
                          KEY_LED_AUX = 0;
                      }
                      else
                      {
                          KEY_LED_AUX = 1;
                      }
                  }
                  else
                  {
                      breath_aux_cnt = 0;
                      KEY_LED_AUX = 0;
                  }
              
                  if (breath_aux_cnt >= BREATH_CNT_MAX)
                  {
                      breath_aux_cnt = 0;
                      breath_aux_fade++;
                      if (breath_aux_fade >= BREATH_FADE_MAX)
                      {
                          breath_aux_fade = 0;
C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 7   

                          if (breath_aux_dir)
                          {
                              breath_aux_duty--;
                              if (breath_aux_duty == 0)
                              {
                                  breath_aux_dir = 0;
                              }
                          }
                          else
                          {
                              breath_aux_duty++;
                              if (breath_aux_duty >= 100)
                              {
                                  breath_aux_duty = 100;
                                  breath_aux_dir = 1;
                              }
                          }
                      }
                  }
              
              #endif
 386   1      
 387   1      #if SNOOZE_BREATH_ENABLE
 388   1      
 389   1          if (alarm_state)
 390   1          {
 391   2              breath_snooze_cnt++;
 392   2              if (breath_snooze_cnt < breath_snooze_duty)
 393   2              {
 394   3                  LED_SNOOZE = 0;
 395   3              }
 396   2              else
 397   2              {
 398   3                  LED_SNOOZE = 1;
 399   3              }
 400   2          }
 401   1          else
 402   1          {
 403   2              breath_snooze_cnt = 0;
 404   2          }
 405   1      
 406   1          if (breath_snooze_cnt >= BREATH_CNT_MAX)
 407   1          {
 408   2              breath_snooze_cnt = 0;
 409   2              breath_snooze_fade++;
 410   2              if (breath_snooze_fade >= BREATH_FADE_MAX)
 411   2              {
 412   3                  breath_snooze_fade = 0;
 413   3                  if (breath_snooze_dir)
 414   3                  {
 415   4                      breath_snooze_duty--;
 416   4                      if (breath_snooze_duty == 0)
 417   4                      {
 418   5                          breath_snooze_dir = 0;
 419   5                      }
 420   4                  }
 421   3                  else
 422   3                  {
 423   4                      breath_snooze_duty++;
 424   4                      if (breath_snooze_duty >= 100)
 425   4                      {
 426   5                          breath_snooze_duty = 100;
C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 8   

 427   5                          breath_snooze_dir = 1;
 428   5                      }
 429   4                  }
 430   3              }
 431   2          }
 432   1      
 433   1      #endif
 434   1      
 435   1      #if MUSIC_BREATH_ENABLE
              
                  if (music_mode == MODE_AMBIENT)
                  {
                      breath_music_cnt++;
                      if (breath_music_cnt < breath_music_duty)
                      {
                          KEY_LED_MUSIC = 0;
                      }
                      else
                      {
                          KEY_LED_MUSIC = 1;
                      }
                  }
                  else
                  {
                      breath_music_cnt = 0;
                      KEY_LED_MUSIC = 0;
                  }
              
                  if (breath_music_cnt >= BREATH_CNT_MAX)
                  {
                      breath_music_cnt = 0;
                      breath_music_fade++;
                      if (breath_music_fade >= BREATH_FADE_MAX)
                      {
                          breath_music_fade = 0;
                          if (breath_music_dir)
                          {
                              breath_music_duty--;
                              if (breath_music_duty == 0)
                              {
                                  breath_music_dir = 0;
                              }
                          }
                          else
                          {
                              breath_music_duty++;
                              if (breath_music_duty >= 100)
                              {
                                  breath_music_duty = 100;
                                  breath_music_dir = 1;
                              }
                          }
                      }
                  }
              
              #endif
 483   1      
 484   1          if (alarm_state || music_mode == MODE_BT_PAIR)
 485   1          {
 486   2              breath_white_cnt++;
 487   2              if (breath_white_cnt < breath_white_duty)
 488   2              {
C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 9   

 489   3                  LED_WHITE = 0;
 490   3              }
 491   2              else
 492   2              {
 493   3                  LED_WHITE = 1;
 494   3              }
 495   2          }
 496   1          else
 497   1          {
 498   2              breath_white_cnt = 0;
 499   2              // 重置呼吸效果状态，确保下次进入呼吸模式时状态正确
 500   2              breath_white_duty = 0;
 501   2              breath_white_dir = 0;
 502   2              breath_white_fade = 0;
 503   2          }
 504   1      
 505   1          if (breath_bt_cnt >= BREATH_CNT_MAX)
 506   1          {
 507   2              breath_bt_cnt = 0;
 508   2              breath_bt_fade++;
 509   2              if (breath_bt_fade >= BREATH_FADE_MAX)
 510   2              {
 511   3                  breath_bt_fade = 0;
 512   3                  if (breath_bt_dir)
 513   3                  {
 514   4                      breath_bt_duty--;
 515   4                      if (breath_bt_duty == 0)
 516   4                      {
 517   5                          breath_bt_dir = 0;
 518   5                      }
 519   4                  }
 520   3                  else
 521   3                  {
 522   4                      breath_bt_duty++;
 523   4                      if (breath_bt_duty >= 100)
 524   4                      {
 525   5                          breath_bt_duty = 100;
 526   5                          breath_bt_dir = 1;
 527   5                      }
 528   4                  }
 529   3              }
 530   2          }
 531   1      
 532   1          if (breath_white_cnt >= BREATH_CNT_MAX)
 533   1          {
 534   2              breath_white_cnt = 0;
 535   2              breath_white_fade++;
 536   2              if (breath_white_fade >= BREATH_FADE_MAX)
 537   2              {
 538   3                  breath_white_fade = 0;
 539   3                  if (breath_white_dir)
 540   3                  {
 541   4                      breath_white_duty--;
 542   4                      if (breath_white_duty == 0)
 543   4                      {
 544   5                          breath_white_dir = 0;
 545   5                      }
 546   4                  }
 547   3                  else
 548   3                  {
 549   4                      breath_white_duty++;
 550   4                      if (breath_white_duty >= white_duty_config)
C51 COMPILER V9.60.7.0   PWM                                                               08/01/2025 16:40:40 PAGE 10  

 551   4                      {
 552   5                          breath_white_duty = white_duty_config;
 553   5                          breath_white_dir = 1;
 554   5                      }
 555   4                  }
 556   3              }
 557   2          }
 558   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    771    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =     29    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
