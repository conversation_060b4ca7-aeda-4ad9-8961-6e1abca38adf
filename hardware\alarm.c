#include "main.h"

uint8_t scan_alarm_time = 0;
uint8_t alarm_flag = 0;            // 闹钟标志
uint8_t alarm_triggered_today = 0; // 当天闹钟是否已响过

// 处理闹钟响起时的睡眠唤醒逻辑
void handle_alarm_wakeup()
{
    // 记录是否从睡眠状态唤醒
    uint8_t wakeup_from_sleep = (time_state == STATE_SLEEP);

    // 如果当前处于睡眠状态，闹钟响起时需要唤醒
    if (wakeup_from_sleep)
    {
        // 恢复到睡眠前的状态，而不是强制回到实时时钟
        time_state = time_state_before_sleep;
        pwm_led_on_flag = 1;
        pwm_led_off_flag = 0;

        // 启动显示LED渐亮呼吸效果
        display_led_breath_on_flag = 1;
        display_led_breath_off_flag = 0;
        breath_display_on_cnt = 0;
        breath_display_on_duty = 0; // 从0开始渐亮
    }

    // 根据进入睡眠前的氛围灯状态决定闹钟响起时的行为
    if (ambient_light_before_sleep == KEY_LED_LEVEL_0)
    {
        // 进入睡眠前氛围灯是灭的，设置标志等待触摸唤醒
        alarm_wakeup_flag = 1;
        // 如果是从睡眠状态唤醒，设为KEY_LED_LEVEL_1
        if (wakeup_from_sleep)
        {
            ambient_light = KEY_LED_LEVEL_1;
            key_led_light = KEY_LED_LEVEL_1; // 同步按键LED亮度
            white_duty_config = 25;          // 设置为KEY_LED_LEVEL_1对应的亮度
        }
    }
    else
    {
        // 进入睡眠前氛围灯是亮的，直接恢复原亮度等级进行呼吸
        ambient_light = ambient_light_before_sleep;
        key_led_light = ambient_light_before_sleep; // 同步按键LED亮度
        set_white_duty_config_by_ambient_light();
        alarm_wakeup_flag = 0; // 不需要等待触摸唤醒
    }
}

void alarm_wakeup()
{
    uint16_t i = 0;
    uint16_t s = 0;
    uint8_t last_check_hour = 0;
    i = rtc.hour * 60 + rtc.minute;
    s = rtc.alarm_hour * 60 + rtc.alarm_minute;

    // 检测跨天，当小时数从23变为0时，说明是新的一天
    if (rtc.hour == 0 && last_check_hour == 23)
    {
        alarm_triggered_today = 0;
    }

    last_check_hour = rtc.hour;

    if (alarm_flag && !alarm_set_flag)
    {
        if (alarm_state || time_state == STATE_SET_SNOOZE_CNT)
            return;

        // 检查闹钟是否已设置有效时间
        if (rtc.alarm_hour == ALARM_NOT_SET_HOUR && rtc.alarm_minute == ALARM_NOT_SET_MINUTE)
            return;

        if (snooze_state == 0)
        {
            if (i == s && !alarm_triggered_today)
            {
                alarm_state = 1;
                alarm_triggered_today = 1;
                alarm_auto_close_time = 3; // 设置3分钟自动关闭

                // 处理闹钟响起时的睡眠唤醒逻辑
                handle_alarm_wakeup();

                // 发送闹钟响铃信号
                key_data[0] = 0x01;
                key_data[1] = 0x01;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
        }
        else
        {
            if (snooze_time_ended)
            {
                snooze_time_ended = 0;

                alarm_state = 1;
                alarm_auto_close_time = 3; // 贪睡闹钟也设置3分钟自动关闭

                // 处理贪睡闹钟响起时的睡眠唤醒逻辑
                handle_alarm_wakeup();

                // 发送闹钟响铃信号
                key_data[0] = 0x01;
                key_data[1] = 0x01;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
        }
    }
}

// 处理倒计时结束后的标志位并进入实时时钟模式
void countdown_exe()
{
    if (countdown_time_ended)
    {
        countdown_time_ended = 0;
        count_down_flag = 0;

        time_state = STATE_REAL_TIME;
        music_mode = MODE_IDE;

        ble_pair_flag = 0;      // 关闭蓝牙配对标志位
        ble_reconnect_flag = 0; // 关闭蓝牙重连标志位
        led_other_flag = 0;     // 关闭其他标志位

        // 发送挂起信号
        key_data[0] = 0x01;
        key_data[1] = 0x10;
        uart0_send(key_data, 4);
        key_data[0] = 0;
        key_data[1] = 0;
    }
}

// 处理闹钟自动关闭
void alarm_auto_close_exe()
{
    if (alarm_auto_close_flag)
    {
        alarm_auto_close_flag = 0;

        // 3分钟时间到，自动关闭闹钟，不进入贪睡
        alarm_state = 0;
        snooze_state = 0;
        alarm_auto_close_time = 0;
        alarm_triggered_today = 1; // 标记今天的闹钟已响完

        // 清除闹钟唤醒标志，恢复正常的氛围灯控制
        alarm_wakeup_flag = 0;

        // 如果当前在贪睡倒计时显示状态，切换回实时时钟显示
        if (time_state == STATE_SNOOZE_CNT)
        {
            time_state = STATE_REAL_TIME;
        }

        // 重置睡眠计时器，闹钟结束后按正常逻辑40s无操作进入睡眠
        no_action_time = 0;
        enter_sleep_time = 0;

        // 发送关闭闹钟信号
        key_data[0] = 0x01;
        key_data[1] = 0x02;
        uart0_send(key_data, 4);
        key_data[0] = 0;
        key_data[1] = 0;
    }
}

void alarm_task(void)
{
    if (time_state == STATE_POWER_OFF)
        return;

    alarm_wakeup();
    countdown_exe();
    alarm_auto_close_exe();
}