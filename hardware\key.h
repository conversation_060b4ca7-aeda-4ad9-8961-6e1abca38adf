#ifndef __KEY_H__
#define __KEY_H__

#define TIMER_SET_ALARM_ENABLE          1
#define ALL_LONG_PRESS_ALARM_OFF_ENABLE 0

#define KEY_INT                         P5_7
#define PWR_ON                          P1_3
#define VCC_DETECT                      P1_0

#define READ_ADDR                       0X81
#define WRITE_ADDR                      0X80
#define REG_ADDR                        0x08

#define KEY_LONG_LONG_TIME              10000 // 10000ms
#define KEY_LONG_TIME                   1500  // 2000ms
#define KEY_LONG_KEEP_TIME              500   // 500ms
#define KEY_DOUBLE_TIME                 250   // 250ms
#define KEY_FILTER_COUNT                5

#define KEY_VOLUME_DOWN                 0x8000
#define KEY_AUX                         0x4000
#define KEY_POWER                       0x2000
#define KEY_PLAY_PAUSE                  0x1000

#define KEY_AMB                         0x0800
#define KEY_TIMER                       0x0400
#define KEY_MUSIC                       0x0200
#define KEY_BT                          0x0100

#define KEY_VOLUME_UP                   0x0080
#define KEY_SNOOZE                      0x0040

#define KEY_NONE                        0x0000

#define MAX_VOLUME                      16
typedef enum
{
    KEY_TYPE_NONE,     // 无按键
    KEY_TYPE_SHORT,    // 短按
    KEY_TYPE_DOUBLE,   // 双击
    KEY_TYPE_LONG,     // 长按
    KEY_TYPE_LONG_KEEP // 长按保持
} KeyType;

typedef enum
{
    KEY_RELEASE_DETECTED, // 检测到释放
    KEY_PRESS_DETECTED,   // 检测到按下
    KEY_MULTI_PRESS,      // 处理中多次按键
    KEY_LONG_PRESS,       // 长按
    KEY_LONG_LONG_PRESS   // 超长按
} KeyEventState;

typedef struct
{
    uint16_t curr_value;    // 当前按键值
    uint16_t last_value;    // 上一次按键值
    uint16_t key_do_value;  // 按键执行值
    uint16_t long_time;     // 长按时间
    uint16_t interval_time; // 间隔时间
    uint8_t click_count;    // 按键次数计数
    uint8_t lock;           // 按键锁定
    KeyEventState state;    // 当前按键状态
    KeyType type;           // 按键类型
} key_t;

extern uint8_t key_data[3];

extern key_t key;
extern uint8_t power_flag;
extern uint8_t countdown_last_value;
extern uint8_t count_down_flag;
extern uint16_t no_action_time;
extern uint8_t enter_sleep_time;
extern uint8_t vcc_detect_flag;

void key_hw_init(void);
void key_state_init(void);
void key_task(void);
void power_on_exe(void);
void vcc_detect_task(void);

#endif
