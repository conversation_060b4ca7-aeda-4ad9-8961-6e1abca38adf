#ifndef __LED_H__
#define __LED_H__

#define C1                  P1_4
#define C2                  P1_5
#define C3                  P1_6
#define C4                  P1_7

#define S1                  P2_4
#define S2                  P2_5
#define S3                  P2_6
#define S4                  P2_7
#define S5                  P3_6
#define S6                  P3_7
#define S7                  P2_0
#define S8                  P2_1
#define S9                  P2_2

#define KEY_LED_PP          P5_0
#define KEY_LED_VOLUME_UP   P5_1

#define LED_SNOOZE          P5_5
#define KEY_LED_AMB         P5_6

#define KEY_LED_POWER       P4_2
#define KEY_LED_VOLUME_DOWN P4_3

#define KEY_LED_AUX         P3_2
#define KEY_LED_BT          P3_3

#define LED_WHITE           P1_1

#define KEY_LED_TIMER       P0_0
#define KEY_LED_MUSIC       P0_1

#define LED_NUM             31

enum
{
    LED_4G = 0,
    LED_4F,
    LED_4E,
    LED_4D,
    LED_4C,
    LED_4B,
    LED_4A,
    LED_3G,
    LED_3F,
    LED_3E,
    LED_3D,
    LED_3C,
    LED_3B,
    LED_3A,
    LED_2G,
    LED_2F,
    LED_2E,
    LED_2D,
    LED_2C,
    LED_2B,
    LED_2A,
    LED_1G,
    LED_1F,
    LED_1E,
    LED_1D,
    LED_1C,
    LED_1B,
    LED_1A,
    LED_PM,
    LED_DOT,
    LED_ALARM
};

extern uint8_t led_12_24_mode;
extern uint8_t led_other_flag;

extern uint8_t bt_name_num_thousand;
extern uint8_t bt_name_num_hundred;
extern uint8_t bt_name_num_ten;
extern uint8_t bt_name_num_one;

/**
 *  led_other_flag
 *   1000 0000  0x80 AM
 *   0100 0000  0x40 PM
 *   0010 0000  0x20 AUX
 *   0001 0000  0x10 BT
 *   0000 1000  0x08 MOOON
 *   0000 0100  0x04 DOT
 *   0000 0010  0x02 ALARM
 */

void led_init();
void led_task();
void all_led_seg_off();
unsigned char show_alarm_dash_hour(uint8_t state);
unsigned char show_alarm_dash_minute(uint8_t state);
uint8_t show_dash(uint8_t position, uint8_t state);

#endif
