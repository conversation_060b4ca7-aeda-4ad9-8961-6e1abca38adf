#include "main.h"

uint8_t led_state_scan_time = 0;
uint8_t ble_exit_cnt = 0;
uint8_t ble_reconnect_exit_cnt = 0;
uint8_t led_bt_pair_time = 0;

void led_state_task()
{
    if (time_state == STATE_POWER_OFF)
        return;

    if (led_state_scan_time >= 20) // 100*20ms=2s
    {
        led_state_scan_time = 0;
        if (music_mode == MODE_BT_PAIR)
        {
            ble_pair_flag = 1;
            ble_exit_cnt++;
            if (ble_exit_cnt > 90) // 三分钟后退出
            {
                ble_pair_flag = 0;
                ble_exit_cnt = 0;
                music_mode = MODE_IDE;
                time_state = STATE_REAL_TIME;

                // 发送退出指令
                key_data[0] = 0x01;
                key_data[1] = 0x10;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
        }
        else
        {
            ble_exit_cnt = 0;
        }

        if (music_mode == MODE_BT_RECONNECT)
        {
            ble_reconnect_exit_cnt++;
            ble_reconnect_flag = 1;
            if (ble_reconnect_exit_cnt > 15) // 30s无连接进入配对
            {
                ble_reconnect_exit_cnt = 0;
                ble_reconnect_flag = 0;
                ble_pair_flag = 1;

                music_mode = MODE_BT_PAIR;
                time_state = STATE_BT_NUM;

                // 设置LED_WHITE呼吸效果的最大亮度为当前环境灯亮度
                set_white_duty_config_by_ambient_light();

                key_data[0] = 0x01;
                key_data[1] = 0x07;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
        }
        else
        {
            ble_reconnect_exit_cnt = 0;
        }
    }
}

void show_led_state()
{
    if (time_state == STATE_POWER_OFF || time_state == STATE_SLEEP)
    {
        return;
    }
    else
    {
        if (pwm_led_on_flag || pwm_led_off_flag)
            return;

        // 设置倒计时状态下的LED控制
        if (time_state == STATE_SET_COUNT_DOWN)
        {
            // KEY_LED_TIMER 闪烁由 set_timer_flash() 函数控制
            // KEY_LED_VOLUME_DOWN 和 KEY_LED_VOLUME_UP 常亮
            KEY_LED_VOLUME_DOWN = 0;
            KEY_LED_VOLUME_UP = 0;

            // 其他LED熄灭
            KEY_LED_POWER = 1;
            KEY_LED_AUX = 1;
            KEY_LED_BT = 1;
            KEY_LED_AMB = 1;
            KEY_LED_MUSIC = 1;
            KEY_LED_PP = 1;
            LED_SNOOZE = 1;
        }
        // 设置闹钟状态下的LED控制
        else if (time_state == STATE_SET_ALARM_HOUR || time_state == STATE_SET_ALARM_MINUTE)
        {
            // KEY_LED_TIMER, KEY_LED_VOLUME_DOWN, KEY_LED_VOLUME_UP, KEY_LED_PP 常亮
            KEY_LED_TIMER = 0;
            KEY_LED_VOLUME_DOWN = 0;
            KEY_LED_VOLUME_UP = 0;
            KEY_LED_PP = 0;

            // 其他LED熄灭
            KEY_LED_POWER = 1;
            KEY_LED_AUX = 1;
            KEY_LED_BT = 1;
            KEY_LED_AMB = 1;
            KEY_LED_MUSIC = 1;
            LED_SNOOZE = 1;
        }
        else
        {
            // 正常模式下，除了snooze灯之外，其他所有按键LED都常亮

            // BT LED控制 - 特殊处理配对模式的闪烁
            if (music_mode == MODE_BT_PAIR)
            {
                // BT配对模式下，KEY_LED_BT由ble_flash()函数控制闪烁
                // 这里不设置，让闪烁函数控制
            }
            else
            {
                KEY_LED_BT = 0; // 正常模式下常亮
            }

            // TIMER LED控制 - 特殊处理设置模式的闪烁
            if (time_state == STATE_SET_COUNT_DOWN)
            {
                // 设置倒计时模式下，KEY_LED_TIMER由set_timer_flash()函数控制闪烁
                // 这里不设置，让闪烁函数控制
            }
            else
            {
                KEY_LED_TIMER = 0; // 正常模式下常亮
            }

            // 其他LED在正常模式下都常亮
            KEY_LED_POWER = 0;
            KEY_LED_VOLUME_DOWN = 0;
            KEY_LED_VOLUME_UP = 0;
            KEY_LED_AUX = 0;
            KEY_LED_AMB = 0;
            KEY_LED_MUSIC = 0;
            KEY_LED_PP = 0;

            // 只有snooze灯熄灭
            LED_SNOOZE = 1;
        }
    }
}
