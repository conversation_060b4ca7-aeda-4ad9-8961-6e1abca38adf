C51 COMPILER V9.60.7.0   TM52F1386_BSP                                                     07/26/2025 11:36:01 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE TM52F1386_BSP
OBJECT MODULE PLACED IN .\Objects\TM52F1386_bsp.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\user\TM52F1386_bsp.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\ha
                    -rdware) DEBUG OBJECTEXTEND PRINT(.\Listings\TM52F1386_bsp.lst) TABS(2) OBJECT(.\Objects\TM52F1386_bsp.obj)

line level    source

   1          #include "TM52F1386_bsp.h"
   2          #include <REGtenxTM52eF1385.h>
   3          #include <intrins.h>
   4          
   5          /*********************************************************************************************************
             -*
   6          **函数名称  bsp_clock_init()
   7          **函数描述 ：设置内部FRC/2为系统时钟
   8          **输    入 ：无
   9          **输    出 ：无
  10          **说    明 ：快时钟系统主频为 18.432/2M   默认二分频
  11          **********************************************************************************************************
             -/
  12          void bsp_clock_init()
  13          {
  14   1          SELFCK = 0; // 切换到慢时钟
  15   1      
  16   1          // CLKCON = 0x02; // 时钟二分频
  17   1          CLKCON = 0x03; // 时钟一分频
  18   1      
  19   1          STPPCK = 0; // 开启部分模块使用快时钟
  20   1          STPFCK = 0; // 开启快时钟
  21   1          SELFCK = 1; // 切换到快时钟
  22   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =     12    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
