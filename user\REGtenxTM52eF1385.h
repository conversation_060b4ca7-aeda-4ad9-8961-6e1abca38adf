/*
******************************************************************************************************
Name		:TM52eF1386.h
Version		:V1.0
Author		:MFTu
Date		:2021.08.23
******************************************************************************************************
*/
#ifndef __TM52eF1385_H__
#define __TM52eF1385_H__

/*
******************************************************************************************************
BYTE Registers
******************************************************************************************************
*/
sfr 	P0    		= 0x80;			//Port 0 data
	//--------------------------------------------------------------------------------------------
	sbit 		P0_7		= P0^7;
	sbit 		P0_6		= P0^6;
	sbit 		P0_5		= P0^5;
	sbit 		P0_4		= P0^4;
	sbit 		P0_3		= P0^3;
	sbit 		P0_2		= P0^2;
	sbit 		P0_1		= P0^1;
	sbit 		P0_0		= P0^0;
/*
******************************************************************************************************
*/
sfr 	SP   		= 0x81;			//Stack Point
/*
******************************************************************************************************
*/
sfr 	DPL   		= 0x82;			//Data Point Low byte
sfr 	DPH   		= 0x83;   		//Data Point High byte

sfr 	INTPORT		= 0x85;   	//no auto clear after INT return
	#define		P5IF		0x20
	//Port 5 Pin Change Interrupt Flag. Set by H/W while PINMODE=110xb and Pin Change,  write 0 to clear to P5IF (include P57IF~P50IF).

	#define		P4IF		0x10
	//Port4 Pin Change Interrupt Flag. write 0 to clear to P4IF (include P47IF~P40IF).

	#define		P3IF		0x08
	//Port3 Pin Change Interrupt Flag.

	#define		P2IF		0x04
	//Port2 Pin Change Interrupt Flag.

	#define		P1IF		0x02
	//Port1 Pin Change Interrupt Flag.

	#define		P0IF		0x01
	//Port0 Pin Change Interrupt Flag.

sfr 	INTPWM 		= 0x86;   	//Interrupt Enable Control
	//--------------------------------------------------------------------------------------------
	#define	PWM3IF	0x08	//PWM3 Interrupt Flag. 1: interrupt asserted, 0: interrupt not asserted
	#define	PWM2IF	0x04	//PWM2 Interrupt Flag. 1: interrupt asserted, write 0 to clear int flag
	#define	PWM1IF	0x02	//PWM1 Interrupt Flag. 1: interrupt asserted, write 0 to clear int flag
	#define	PWM0IF	0x01	//PWM0 Interrupt Flag. 1: interrupt asserted, write 0 to clear int flag

/*
******************************************************************************************************
*/
sfr 	PCON  		= 0x87;			//不支持位操作,用读改写方式
	//--------------------------------------------------------------------------------------------
	#define 	SMOD	0x80		//PCON Bit[7],1=波特率倍增
	//--------------------------------------------------------------------------------------------
	#define 	GF1		0x08		//PCON Bit[3],用户可以随便使用
	#define 	GF0		0x04		//PCON Bit[2],用户可以随便使用
	//--------------------------------------------------------------------------------------------
	#define 	PD		0x02		//PCON Bit[1],此位置1,芯片进入stop模式
	//--------------------------------------------------------------------------------------------
	#define 	IDL		0x01		//PCON Bit[0],此位置1,芯片进入IDL模式
	//---------------------------------------------------------------------------------------------
	#define		ENTER_IDLE_MODE		PCON=(PCON&0xfe)|IDL
	#define		LEAVE_IDLE_MODE		PCON=(PCON&0xfe)
	#define		ENTER_STOP_MODE		PCON=(PCON&0xfd)|PD
	#define		LEAVE_STOP_MODE		PCON=(PCON&0xfd)
	
/*
******************************************************************************************************
*/
sfr 	TCON  		= 0x88;
	//--------------------------------------------------------------------------------------------
	sbit 		TF1 		= TCON^7;	//定时器T1溢出标志,可由程序查询和清零,TF1也是中断请求源,当CPU响应T1中断时由硬件清零
	sbit 		TR1 		= TCON^6;	//T1充许计数控制位,为1时充许T1计数
	//--------------------------------------------------------------------------------------------
	sbit 		TF0 		= TCON^5;	//定时器T0溢出标志,可由程序查询和清零,TF0也是中断请求源,当CPU响应T0中断时由硬件清零
	sbit 		TR0			= TCON^4;	//T0充许计数控制位,为1时充许T0计数
	//--------------------------------------------------------------------------------------------
	sbit 		IE1 		= TCON^3;	//外部中断1请示源(INT1,P3.3)标志;IE1也是中断请求源,当CPU响应该中断时由硬件清IE1(边沿触发方式)
	sbit 		IT1 		= TCON^2;	//外部中断源1触发方式控制位,IT1＝0,外部中断1程控为电平触发方式;当INT1（P3.3）输入低电平时,置位IE1
	//--------------------------------------------------------------------------------------------
	sbit 		IE0 		= TCON^1;	//外部中断0请示源(INT1,P3.2)标志;IE0也是中断请求源,当CPU响应该中断时由硬件清IE0(边沿触发方式)
	sbit 		IT0 		= TCON^0;	//外部中断源0触发方式控制位,IT0＝0,外部中断0程控为电平触发方式;当INT0（P3.2）输入低电平时,置位IE0
/*
******************************************************************************************************
*/
sfr 	TMOD  		= 0x89;				//不支持位操作,用读改写方式
	//--------------------------------------------------------------------------------------------
	#define 	T1_Mask		~0xf0		//T1工作模式选择Mask
	//--------------------------------------------------------------------------------------------
	#define 	GATE1		0x80		//定时器1操作开关控制位
					 	 				//当GATE1=1时,INT1引脚为高电平,同时TCON中的TR1控制位为1时,计时/计数器1才开始工作
					 	 				//当GATE1=0时,则只要将TR1控制位设为1，计时/计数器1就开始工作
	#define 	CT1			0x40		//定时/计数器1功能的选择位
					 	 				//CT1=1为计数器,通过外部引脚T1输入计数脉冲
					 	 				//CT1=0为定时器,由内部系统时钟提供计时工作脉冲
	//--------------------------------------------------------------------------------------------
	#define 	T1_Mode0		0x00		//T1为13位计时/计数器
	#define 	T1_Mode1		0x10		//T1为16位计时/计数器
	#define 	T1_Mode2		0x20		//T1为8位自动加载计数/计时器 
	#define 	T1_Mode3		0x30		//timer stop
	//--------------------------------------------------------------------------------------------
	#define 	T0_Mask			~0x0f		//T0工作模式选择Mask
	//--------------------------------------------------------------------------------------------
	#define 	GATE0			0x08		//定时器0操作开关控制位
					 	 				//当GATE0=1时,INT0引脚为高电平,同时TCON中的TR0控制位为1时,计时/计数器0才开始工作
					 	 				//当GATE0=0时,则只要将TR0控制位设为1，计时/计数器0就开始工作
	#define 	CT0				0x04		//定时/计数器0功能的选择位
					 	 				//CT0=1为计数器,通过外部引脚T0输入计数脉冲
					 	 				//CT0=0为定时器,由内部系统时钟提供计时工作脉冲
	//--------------------------------------------------------------------------------------------
	#define 	T0_Mode0			0x00		//T0为13位计时/计数器 
	#define 	T0_Mode1			0x01		//T0为16位计时/计数器 
	#define 	T0_Mode2			0x02		//T0为8位自动加载计数/计时器
	#define 	T0_Mode3			0x03		//仅适用于T0,T0分为两个独立的8位定时器/计数器TH0及TL0,在此模式下T1停止工作
/*
******************************************************************************************************
*/	
sfr 	TL0   		= 0x8A;				//Timer 0 data low byte
sfr 	TH0   		= 0x8C;				//Timer 0 data high byte
/*
******************************************************************************************************
*/
sfr 	TL1   		= 0x8B;				//Timer 1 data low byte
sfr 	TH1   		= 0x8D;			  //Timer 1 data high byte
/*
******************************************************************************************************
*/
sfr 	SCON2  		= 0x8E;			  //
	#define		SM				0x80
	#define		SM2S			0x80
	#define 	UART2_9Bits		SCON2=(SCON2&0x7f)|SM
	#define 	UART2_8Bits		SCON2=(SCON2&0x7f)

	#define		REN2			0x10
	#define 	UART2_RECEPTION_ENABLE		SCON2=(SCON2&0xef)|REN2
	#define 	UART2_RECEPTION_DISABLE		SCON2=(SCON2&0xef)

	#define		TB82		0x08	//Uart2 transmit Bit 8
	#define		RB82		0x04	//Uart2 Receive Bit 8
	#define		TI2			0x02	//Uart2 Transmit interrupt flag
	#define		RI2			0x01	//Uart2  receive interrupt flag
/*
******************************************************************************************************
*/
sfr 	SBUF2  		= 0x8F;			//Uart2 transmit and receive data

/*
******************************************************************************************************
*/
sfr 	P1    		= 0x90;				//Port 1 data
	//--------------------------------------------------------------------------------------------
	sbit 		P1_7		= P1^7;
	sbit 		P1_6		= P1^6;	
	sbit 		P1_5		= P1^5;	
	sbit 		P1_4		= P1^4;	
	sbit 		P1_3		= P1^3;	
	sbit 		P1_2		= P1^2;	
	sbit 		P1_1		= P1^1;	
	sbit 		P1_0		= P1^0;	
/*
******************************************************************************************************
*/
sfr		PORTIDX		= 0x91;
	//PORTIDX:PORTIDX,Bit[2:0],	  Pin Mode Index
								//000~101: Port0~Port5 Pin Mode
								//others: Reserved"

/*
******************************************************************************************************
*/
sfr 	OPTION    	= 0x94;

	//--------------------------------------------------------------------------------------------
	//TM3CKS:OPTION Bit[7:6],"Timer3 Clock Source Select. 
							//00: SLOW (SXT/SRC), 01: FRC/512 (36KHz)
							//10: SLOW/2 (SXT/SRC), 11: FRC/1024"
	
	//--------------------------------------------------------------------------------------------
	//WDTPSC:OPTION Bit[5:4],WatchDog Timer Prescale. 3=Fastest
	#define		WDTPSC_1	  0x00	  //Watch Dog Timer Prescale 3=Fastest   
	#define		WDTPSC_2		0x10		//Watch Dog Timer Prescale 3=Fastest   
	#define		WDTPSC_3 		0x20		//Watch Dog Timer Prescale 3=Fastest   
	#define		WDTPSC_4 		0x30		//Watch Dog Timer Prescale 3=Fastest   
	
	#define		WDTPSC_00		0x00	  	//Watch Dog Timer Prescale 0  
	#define		WDTPSC_01		0x10		//Watch Dog Timer Prescale 1   
	#define		WDTPSC_02 		0x20		//Watch Dog Timer Prescale 2   
	#define		WDTPSC_03		0x30		//Watch Dog Timer Prescale 3
		
	#define  	WDTPSC0    OPTION=OPTION&0xcf			//Watch Dog Timer Prescale 3=Fastest   
	#define  	WDTPSC1    OPTION=(OPTION&0xcf)|0x10	//Watch Dog Timer Prescale 3=Fastest   
	#define  	WDTPSC2    OPTION=(OPTION&0xcf)|0x20	//Watch Dog Timer Prescale 3=Fastest   
	#define  	WDTPSC3    OPTION=(OPTION&0xcf)|0x30	//Watch Dog Timer Prescale 3=Fastest   
	
	//--------------------------------------------------------------------------------------------
	//ADCKS:OPTION Bit[3:2],ADC Clock Rate 
	#define		ADCKS_Mask			0x0C	//b'00001100'		//ADC Clock Rate Mask
	#define		ADCKS_sys_32	  0x00	    //Interrupt rate is 32768 system clock cycle
	#define		ADCKS_sys_16		0x04		//Interrupt rate is 16384 system clock cycle
	#define		ADCKS_sys_8 		0x08		// Interrupt rate is 8192 system clock cycle
	#define		ADCKS_sys_4 		0x0c		//Interrupt rate is 128 system clock cycle
	//--------------------------------------------------------------------------------------------
	//TM3PSC:OPTION Bit[1:0],Timer3 Int Prescale. 0=65536*TM3Clk, 1=16384*TM3Clk, 2=4096*TM3Clk, 3=1024*TM3CLK
	#define		TM3_InterruptRateMask			0x03		//b'00000011'		//TM3_InterruptRateMask
	#define		TM3_InterruptIsCountDataOverflow	0x00	    //Interrupt rate is 32768 Slow clock cycle
	#define		TM3_InterruptIs1SecondRate		0x01		//Interrupt rate is 16384 Slow clock cycle
	#define		TM3_InterruptIs1_2SecondRate		0x02		// Interrupt rate is 8192 Slow clock cycle
	#define		TM3_InterruptIs1_4SecondRate		0x03		//Interrupt rate is 128 Slow clock cycle
/*
******************************************************************************************************
*/
sfr 	INTFLG    	= 0x95;
	//--------------------------------------------------------------------------------------------
	#define		LVDIF	0x80		//Low Voltage Detect Flag, write 0 to clear int flag
	//--------------------------------------------------------------------------------------------
	#define		TKIFA	0x20		//Touch Key 中断标志(INTVEC=53h)，HW clear while TKSOC set
	//--------------------------------------------------------------------------------------------
	#define		ADIF	0x10		//ADC 中断标志，HW clear while ADSOC set
	//--------------------------------------------------------------------------------------------
	#define 	PCIF	0x02		//P1端口中断标志，1=P1端口中断(INTVEC=43h)
	//--------------------------------------------------------------------------------------------
	#define 	TF3		0x01		//TM3中断标志,1=TM3中断 (INTVEC=3Bh)
/*
******************************************************************************************************
*/
sfr		INTPIN		= 0x96;						//P1端口中断唤醒设置，Bit=1有效
	#define		PIN7IF		0x80
	//"Px7IF Pin change interrupt flag, x define bye PORTIDX, 
	//write 0 to clear Px.7 int flg"

	#define		PIN6IF		0x40
	//"Px6IF Pin change interrupt flag, x define bye PORTIDX, 
	//write 0 to clear Px.6 int flg"
	
	#define		PIN5IF		0x20
	//"Px5IF Pin change interrupt flag, x define bye PORTIDX,
	//write 0 to clear Px.5 int flg"

	#define		PIN4IF		0x10
	//"Px4IF Pin change interrupt flag, x define bye PORTIDX, 
	//write 0 to clear Px.4 int flg"

	#define		PIN3IF		0x08
	//"Px3IF Pin change interrupt flag, x define bye PORTIDX, 
	//write 0 to clear Px.3 int flg"

	#define		PIN2IF		0x04
	//"Px2IF Pin change interrupt flag, x define byePORTIDX, 
	//write 0 to clear Px.2 int flg"

	#define		PIN1IF		0x02
	//"Px1IF Pin change interrupt flag, x define bye PORTIDX, 
	//write 0 to clear Px.1 int flg"

	#define		PIN0IF		0x01
	//"Px0IF Pin change interrupt flag, x define bye PORTIDX, 
	//write 0 to clear Px.0 int flg"

/*
******************************************************************************************************
*/
sfr		SWCMD		= 0x97;			//Write 56h to this register will generate SW Reset 
									//Write 65h to this register to enable IAP all area, other value to disable IAP all

	#define		WDTO	0x02		//WatchDog Time-Out flag
	#define		IAPEN	0x01		//IAP Enable Control
/*
******************************************************************************************************
*/
sfr 	SCON  		= 0x98;
	//--------------------------------------------------------------------------------------------
	sbit 		SM0 		= SCON^7;	//串行口工作方式控制位 Bit[0]
	sbit 		SM1 		= SCON^6;	//串行口工作方式控制位 Bit[1]
	#define		Uart_ModeMask	~0xC0	//串口模式Mask
	#define		Uart_Mode0		0x00	//00 方式0－波特率由振荡器频率所定:振荡器频率/12
	#define		Uart_Mode1		0x40	//01 方式1－波特率由定时器T1或T2的溢出率和SMOD所定:2SMOD*(T1溢出率)/32
	#define		Uart_Mode2		0x80	//10 方式2－波特率由振荡器频率和SMOD所定:2SMOD*振荡器频率/64
	#define		Uart_Mode3		0xc0	//11 方式3－波特率由定时器T1或T2的溢出率和SMOD所定:2SMOD*(T1溢出率)/32
	
	//--------------------------------------------------------------------------------------------
	sbit 		SM2 		= SCON^5;	//多机通信控制位*/
										//多机通信是工作于方式2和方式3，SM2位主要用于方式2和方式3接收状态
					 	 				//当串行口工作于方式2或3:
					 	 				//当SM2=1时,只有当接收到第9位数据(RB8)为1时,才把接收到的前8位数据送入SBUF,且置位RI发出中断申请,否则会将接收到的数据放弃
					 	 				//当SM2=0时,就不管第9位数据是0还是1,都得数据送入SBUF,并发出中断申请,工作于方式0时，SM2必须为0
	//--------------------------------------------------------------------------------------------
	sbit 		REN 		= SCON^4;	//允许接收位:
					 	 				//REN用于控制数据接收的允许和禁止,REN=1时,允许接收,REN=0时,禁止接收
	//--------------------------------------------------------------------------------------------
	sbit 		TB8 		= SCON^3;	//发送数据第8位
	sbit 		RB8 		= SCON^2;	//接收数据第8位
	//--------------------------------------------------------------------------------------------
	sbit 		TI 			= SCON^1;	//发送中断标志位：
					 	 				//方式0时,发送完第8位数据后,由硬件置位
					 	 				//其它方式下,由硬件置位,TI=1表示帧发送结束,TI可由软件清零
	//--------------------------------------------------------------------------------------------
	sbit 		RI 			= SCON^0;	//接收中断标志位：
					 	 				//方式0时,接收完第8位数据后,由硬件置位
					 	 				//其它方式下,由硬件置位,RI=1表示帧接收完成
/*
******************************************************************************************************
*/
sfr 	SBUF  		= 0x99;				//串口缓存寄存器,UART transmit and receive data

/*
******************************************************************************************************
*/
sfr 	SCON1  		= 0x9A;
	#define		SM1S		0x80
	//Uart1 mode select :  0: 8bit Uart, Baud Rate is variable   1:9bit Uart, Baud Rate is variable

	#define		REN1		0x10
	//Uart1 reception enable

	#define		TB81		0x08
	//Uart1 transmit Bit 8
	
	#define		RB81		0x04
	//Uart1 Receive Bit 8
	
	#define		TI1			0x02
	//Uart1 Transmit interrupt flag
	
	#define		RI1			0x01
	//Uart1  receive interrupt flag

/*
******************************************************************************************************
*/
sfr		SBUF1		= 0x9B;	//Uart1 transmit and receive data

/*
******************************************************************************************************
*/
sfr		TKCON3		= 0x9C;	
	#define		TKPDB		0x80
	//Touch Key Power Down
	
	#define		TKEOCB		0x40
	//Touch Key End of Conversion
	
	#define		TKIFB		0x20
	//Touch Key B INT Flag (INTVEC=53h), HW clear while TKSOC set

	#define		TKXCAPB		0x10
	//Touch Key XCLD selection. 0: disable XCLD, 1: enable XCLD

	//JMPVALB:TKCONB,Bit[3:1],TK Clock frequency select. 000: slow, 111: fast

	#define		SPREAD		0x01	//0: disable
									//1:enable TK spread spectrum (展頻)

/*
******************************************************************************************************
*/
sfr		PWM2CON		= 0x9D;	
	//PWM2OM:PWM2CON,Bit[7:6],"PWM2 output mode.00 ~ 11: Mode0 ~ Mode3"

	//PWM2DZ:PWM2CON,Bit[5:0],
		//"PWM2 Dead Zone.
		//0000: dead zone disable
		//0001: dead zone 1*Tpwmclk
		//…
		//111111: dead zone 63*Tpwmclk"

/*
******************************************************************************************************
*/
sfr		PWMIDX		= 0x9E;	
	//PWMIDX:PWMIDX,Bit[7:0],
	//PWM0~3 Period/Duty Index
	//PWMIDX:
	//0xh: PWM0 Period/Duty access
	//1xh: PWM1 Period/Duty access
	//2xh: PWM2 Period/Duty access
	//3xh: PWM30~PWM35 Period/Duty access
	//...
	//30h: PWM30 Period/Duty access
	//31h: PWM31 Period/Duty access
	//32h: PWM32 Period/Duty access
	//33h: PWM33 Period/Duty access
	//34h: PWM34 Period/Duty access
	//35h: PWM35 Period/Duty access"

	
//****************************************************************************************************
sfr		PWMEN		= 0x9F;
	#define	PWM3IE	0x80		
	//PWM3 Interrupt Enable. 0: Disable, 1: Enable

	#define	PWM2IE	0x40
	//PWM2 Interrupt Enable. 0: Disable, 1: Enable

	#define	PWM1IE	0x20
	//PWM1 Interrupt Enable. 0: Disable, 1: Enable

	#define	PWM0IE	0x10
	//PWM0 Interrupt Enable. 0: Disable, 1: Enable
	
	#define	PWM3EN	0x08		//PWM3 enable
	#define	PWM2EN	0x04		//PWM2 enable
	#define	PWM1EN	0x02		//PWM1 enable
	#define	PWM0EN	0x01		//PWM0 enable

/*
******************************************************************************************************
*/
sfr 	P2    		= 0xA0;			//Port 2 data
	//--------------------------------------------------------------------------------------------
	sbit 		P2_7		= P2^7;	
	sbit 		P2_6		= P2^6;	
	sbit 		P2_5		= P2^5;	
	sbit 		P2_4		= P2^4;	
	sbit 		P2_3		= P2^3;	
	sbit 		P2_2		= P2^2;	
	sbit 		P2_1		= P2^1;	
	sbit 		P2_0		= P2^0;							
/*
******************************************************************************************************
*/
sfr		PWMCON		= 0xA1;

	//
	//PWM3CKS:PWMCON Bit[7:6],PWM3 Clock source. 3=IRC18M*2, 2=IRC18M, 1=Cpuclk, 0=Cpuclk

	//
	//PWM2CKS:PWMCON Bit[5:4],PWM2 Clock source. 3=IRC18M*2, 2=IRC18M, 1=Cpuclk, 0=Cpuclk

	//
	//PWM1CKS:PWMCON Bit[3:2],PWM1 Clock source. 3=IRC18M*2, 2=IRC18M, 1=Cpuclk, 0=Cpuclk

	//
	//PWM0CKS:PWMCON Bit[1:0],PWM0 Clock source. 3=IRC18M*2, 2=IRC18M, 1=Cpuclk, 0=Cpuclk
/*
******************************************************************************************************
*/
sfr		PINMOD10		= 0xA2;
	//--------------------------------------------------------------------------------------------
	//PINMOD1:PINMOD10 Bit[7:4],Px.1 Pin Control, x define by PORTIDX

	//--------------------------------------------------------------------------------------------
	//PINMOD0:PINMOD10 Bit[3:0],Px.0 Pin Control, x define by PINIDX

/*
******************************************************************************************************
*/
sfr		PINMOD32		= 0xA3;
	//--------------------------------------------------------------------------------------------
	//PINMOD3:PINMOD32 Bit[7:4],Px.3 Pin Control, x define by PINIDX

	//--------------------------------------------------------------------------------------------
	//PINMOD2:PINMOD32 Bit[3:0],Px.2 Pin Control, x define by PINIDX
/*
******************************************************************************************************
*/
sfr		PINMOD54		= 0xA4;
	//--------------------------------------------------------------------------------------------
	//PINMOD5:PINMOD54 Bit[7:4],Px.5 Pin Control, x define by PINIDX

	//--------------------------------------------------------------------------------------------
	//PINMOD4:PINMOD54 Bit[3:0],Px.4 Pin Control, x define by PINIDX

/*
******************************************************************************************************
*/
sfr		PINMOD76		= 0xA5;
	//--------------------------------------------------------------------------------------------
	//PINMOD7:PINMOD76 Bit[7:4],Px.7 Pin Control, x define by PINIDX

	//--------------------------------------------------------------------------------------------
	//PINMOD6:PINMOD76 Bit[3:0],Px.6 Pin Control, x define by PINIDX

/*
******************************************************************************************************
*/
sfr 	PINMODE  		= 0xA6;
	
	#define		VBGEN		0x80
	//0: VBG/VBGO disable at IDLE and STOP mode
	//1: Force VBG/VBGO to be enabled, included in IDLE mode, but disabled in STOP mode"
	
	#define		UART1PS		0x20
	//UART1 Pin Select
	//0: RXD1/TXD1 = P3.6/P3.7
	//1: RXD1/TXD1 = P5.4/P5.5"
	
	#define		PSEUDOEN	0x10
	//P30~P32 pseudo open-drain 
	//0: disable
	//1: enable"

	//--------------------------------------------------------------------------------------------
	//I2CPS:PINMODE Bit[3:2],
	//I2C Pin Select
	//00: SCL/SDA = P3.4/P3.5
	//01: SCL/SDA = P3.0/P3.1
	//1x: SCL/SDA = P0.2/P0.3"

	//--------------------------------------------------------------------------------------------
	//UART0PS:PINMODE Bit[1:0],
	//UART0 Pin Select
	//00: RXD0/TXD0 = P3.0/P3.1
	//01: RXD/TXD =  P3.4/P3.5
	//10: RXD/TXD =  P4.4/P4.5
	//11: reservd"


	
//------------------------------------------------------------------------------------------------------------
sfr		TKCHSA			=0xA7;
	//--------------------------------------------------------------------------------------------
	//TKCHSA:TKCHSA Bit[4:0],Touch Key Channel Select

/*
******************************************************************************************************
*/
sfr 	IE    		= 0xA8;
	//--------------------------------------------------------------------------------------------
	sbit 		EA 			= IE^7;		//EA=0时,所有中断禁止;EA=1时,各中断的产生由个别的允许位决定
	sbit		ET2			= IE^5;		//定时2中断允许
	sbit 		ES 			= IE^4;		//串行口中断充许(ES=1充许,ES=0禁止)
	sbit 		ET1 		= IE^3;		//定时1中断充许
	sbit 		EX1 		= IE^2;		//外中断INT1中断充许
	sbit 		ET0 		= IE^1;		//定时器0中断充许
	sbit 		EX0 		= IE^0;		//外部中断INT0的中断允许
/*
******************************************************************************************************
*/
sfr		INTE1		= 0xA9;
	//--------------------------------------------------------------------------------------------
	#define		PWMIE		0x80		//PWM interrupt Enable
	//--------------------------------------------------------------------------------------------
	#define		I2CE		0x40		//I2C interrupt Enable
	//--------------------------------------------------------------------------------------------
	#define		ES2			0x20		//Uart1/2 Interrupt Enable 中断允许
	//--------------------------------------------------------------------------------------------
	#define		SPIE		0x10		//SPI 中断允许
	//--------------------------------------------------------------------------------------------
	#define		ADTKIE 		0x08		//ADC/Touch Key  中断允许
	//--------------------------------------------------------------------------------------------
	//--------------------------------------------------------------------------------------------
	#define		LVDIE		0x04		//LVD Interrupt Enable
	//--------------------------------------------------------------------------------------------
	#define	 	PCIE		0x02		//Pin Change Interrupt Enable
	//--------------------------------------------------------------------------------------------
	#define		TM3IE		0x01		//TM3中断允许		
/*
******************************************************************************************************
*/
sfr		ADCDL		= 0xAA; //ADTKDT修改後
	//--------------------------------------------------------------------------------------------
	//ADCDL:ADCDL Bit[7:4],ADC Data 3~0
/*
******************************************************************************************************
*/
sfr		ADCDH		= 0xAB;//
	//--------------------------------------------------------------------------------------------
	//ADCDH:ADCDH Bit[7:0],ADC Data 11~4

/*
******************************************************************************************************
*/
sfr		TKCHSB		= 0xAC;//
	//--------------------------------------------------------------------------------------------
	//TKCHSB:TKCHSB Bit[4:0],Touch Key Channel Select

/*
******************************************************************************************************
*/
sfr		TKCON		= 0xAD;
	//--------------------------------------------------------------------------------------------
	#define		TKPDA		0x80		
	//Touch Key Power Down

	#define		TKEOCA		0x40		
	//Touch Key End of Conversion
	
	#define		TKRERUN		0x20		
	//"0: Auto re-start disable. TKSOC needs to be executed once for each TK conversion
	//1: Auto re-start enable. After TKSOC is executed once, TK will be converted continuously without re-executing TKSOC"

	#define 	TKIVCS		0x10	
	//TK voltage select
	//0: VCHG=2.8V; VINT=1.4V
	//1: VCHG=3.6V; VINT=1.8V

	#define 	TKXCAPA		0x08	
	//0: XCAPA disable
	//1: XCAPA enable

	#define 	TKOFFSET	0x04
	//0: Idle TKVD connect to VSS
	//1: Idle TKVD connect to AC shielding (=VSS @EOC)	
	
	
	//--------------------------------------------------------------------------------------------
	//ATKMODE:TKCON Bit[1:0],
	//"Touch Key Scan Mode
	//00: scan TKCHS and rest ATKCH setting channel, max=29
    //  TKD0=TKRAM[0], TKD27=TKRAM[27], TKD31=TKRAM[31]
	//01: scan TKCHS and rest ATKCH setting channel, each ch 2 times, max=16
    //  1st_ch=TKRAM[1:0], 2nd_ch=TKRAM[3:2], 16th_ch=TKRAM[31:30]
	//10: scan TKCHS and rest ATKCH setting channel, each ch 4 times, max=8
    //  1st_ch=TKRAM[3:0], 2nd_ch=TKRAM[7:4], th_ch=TKRAM[31:28]
	//11: scan TKCHS and rest ATKCH setting channel, each ch 8 times, max=4
    //  1st_ch=TKRAM[7:0], 2nd_ch=TKRAM[15:8], 4th_ch=TKRAM[31:24]"

	
/*
******************************************************************************************************
*/
sfr		CHSEL		= 0xAE;//Touch Key Counter Data 7~0
	//--------------------------------------------------------------------------------------------
	//ADCVREFS:CHSEL Bit[7:6],
		//00: VCC
		//01: 2.51V
		//10: 3.01V
		//11: 4.02V"

	//--------------------------------------------------------------------------------------------
	//ADCHS:CHSEL Bit[5:0],"ADC Channel Select
		//000000: AD0
		//000001: AD1
		//...
		//101001: AD41
		//
		//10_1010: VTEMP
		//10_1011: VBG
		//10_1100: 1/4VCC"
	

               
/*
******************************************************************************************************
*/
sfr		ATKCHB2		= 0xAF;
	//--------------------------------------------------------------------------------------------
	//ATKCHB2:ATKCHB2 Bit[7:0],
		//"Auto Touch Key TKB16~TKB20 Channel Select
		//0: Disable auto scan
		//1: Enable auto scan"


	
/*
******************************************************************************************************
*/
sfr 	P3    		= 0xB0;				//Port 3 data
	//--------------------------------------------------------------------------------------------
	sbit 		P3_7		= P3^7;
	sbit 		P3_6		= P3^6;
	sbit 		P3_5		= P3^5;
	sbit 		P3_4		= P3^4;
	sbit 		P3_3		= P3^3;
	sbit 		P3_2		= P3^2;
	sbit 		P3_1		= P3^1;
	sbit 		P3_0		= P3^0;
/*
******************************************************************************************************
*/
sfr		LXDCON		= 0xB1;
	#define		LXDON		0x80
	//LCD/LED Enable
	
	//--------------------------------------------------------------------------------------------
	//LXDDUTY:LXDCON Bit[6:4],
	
	#define		LEDBRITM	0x08
	//"LED brightness mode
	//0: Uniform brightness mode (亮度均勻模式) (IO drive with current source)
	//1: Brightness enhancement mode (亮度增強模式) (IO drive without current source)"

	//LXDBRIT:LXDCON Bit[2:0],
		//"LCD/LED Brightness. 0: Most darkness, 1: Most brightness
		//000: 1/128
		//001: 2/128
		//010: 4/128
		//011: 8/128
		//100: 16/128
		//101: 32/128
		//110: 64/128
		//111: 125/128"

	
/*
******************************************************************************************************
*/
sfr		LXDCON2		= 0xB2;
	
	#define		LCDCKS		0x80	
	//"LCD Clock Source, 0: SIRC/4, 1: SXT/2, only for LCD Mode
	//LED Clock Source FRC"

	//--------------------------------------------------------------------------------------------
	//LXDPSC:LXDCON2 Bit[6:5],
		//LCD Clock divided by, 0: 64, 1: 32, 2: 16, 3: 8

	#define		SELLED		0x10	
	//Select LED. 0: LCD, 1: LED

	#define		LEDHOLD		0x08
	//"LED Hold
	//0: LED Scan
	//1: LED Hold"
	
	//--------------------------------------------------------------------------------------------
	//LEDMODE:LXDCON2 Bit[1:0],
		//"LED Mode
		//00: 傳統掃
		//01: reservd
		//10: BD 掃"

/*
******************************************************************************************************
*/
sfr 	TKTMRL 		= 0xB4;
//TKTMRL:TKTMRL Bit[7:0],TK Module0 Scan length 7~0 adjustment. 000: shortest, FFF: longest

/*
******************************************************************************************************
*/
sfr 	TKCON2 		= 0xB5;

	#define		TKFJMP		0x80
	//"TK Clock frequency auto-change selection
	//0: TK Clock freq. define by JMPVAL
	//1: TK Clock freq  auto-change"

	//JMPVALA:TKCON2 Bit[6:4],
	//TKA Clock frequency select. 00: slow, 11: fast

	//TKTMRH:TKCON2 Bit[3:0],TK Module0 Scan length 9~8 adjustment.

/*
******************************************************************************************************
*/
sfr 	ATKCHB1 	= 0xB6;
	//ATKCHB1:ATKCHB1 Bit[7:0],"Auto Touch Key TKB08~TKB15 Channel Select
							//0: Disable auto scan
							//1: Enable auto scan"

/*
******************************************************************************************************
*/
sfr 	ATKCHB0 	= 0xB7;
	//ATKCHB0:ATKCHB0 Bit[7:0],"Auto Touch Key TKB00~TKB07 Channel Select
							//0: Disable auto scan
							//1: Enable auto scan"

/*
******************************************************************************************************
*/
sfr 	IP    		= 0xB8;
	//--------------------------------------------------------------------------------------------
	sbit		PT2			= IP^5;		//定时2中断优先级低位
	sbit 		PS			= IP^4;		//串行口中断优先级低位
	sbit 		PT1	    	= IP^3;		//定时1中断优先级低位
	sbit 		PX1	    	= IP^2;		//外中断INT1中断优先级低位
	sbit 		PT0	    	= IP^1;		//定时0中断优先级低位
	sbit 		PX0	    	= IP^0;		//外中断INT0中断优先级低位
/*
******************************************************************************************************
*/
sfr 	IPH    		= 0xB9;
	//--------------------------------------------------------------------------------------------
//	sbit		PT2H		  = IP^5;		//定时2中断优先级高位
//	sbit 		PSH			  = IP^4;		//串行口中断优先级高位
//	sbit 		PT1H	    = IP^3;		//定时1中断优先级高位
//	sbit 		PX1H	    = IP^2;		//外中断INT1中断优先级高位
//	sbit 		PT0H	    = IP^1;		//定时0中断优先级高位
//	sbit 		PX0H	    = IP^0;		//外中断INT0中断优先级高位/*
	#define		IPH_Mask	  ~0x3F		//
	#define		IPH_PT2H    0x20		//定时2中断优先级高位       
	#define		IPH_PSH     0x10 	  //串行口中断优先级高位      
	#define		IPH_PT1H    0x08	  //定时1中断优先级高位       
	#define		IPH_PX1H    0x04		//外中断INT1中断优先级高位  
	#define		IPH_PT0H    0x02		//定时0中断优先级高位       
	#define		IPH_PX0H    0x01 	  //外中断INT0中断优先级高位/*
/*
******************************************************************************************************
*/	
sfr 	IP1  		= 0xBA;
	//--------------------------------------------------------------------------------------------
	#define		PPWM		0x80		//PWM Interrupt Priority Low bit
	#define		PI2C		0x40		//I2C Interrupt Priority Low bit
	#define		PS2			0x20		//UART2中断优先级低位
	#define		PSPI		0x10		//SPI中断优先级低位
	#define 	PADTKI		0x08		//ADC/Touch Key 中断优先级低位
	#define		PLVD		0x04		//LVD Interrupt Priority Low bit
	#define 	PPC			0x02		//Pin Change Interupt Priority Low bit
	#define		PT3			0x01		//定时3中断优先级低位
/*
******************************************************************************************************
*/
sfr 	IP1H   		= 0xBB;
	//--------------------------------------------------------------------------------------------
	#define		PPWMH 		0x80		//PWM Interrupt Priority High bit
	#define		PI2CH 		0x40		//I2C Interupt Priority High bit
	#define		PS2H 		0x20		//UART2中断优先级高位
	#define		PSPIH 		0x10		//SPI中断优先级高位
	#define 	PADTKIH		0x08		//ADC/Touch Key 中断优先级高位
	#define		PLVDH		0x04		//XINT2 to XINT9  Interupt Priority High bit
	#define 	PPCH		0x02		//Pin Change Interupt Priority High bit
	#define 	PT3H    	0x01		//定时3中断优先级高位
/*
******************************************************************************************************
*/
sfr   SPCON     = 0xBC;
	#define		SPCON_Mask	~0xFF		//
	#define		SPEN    0x80		//SPI Enable.0: SPI Enable;1: SPI Disable
	#define		MSTR    0x40 	  //Master Mode Enable.0: Slave Mode;1: Master Mode
	#define		CPOL    0x20	  //SPI Clock Polarity.0: SCK is low in idle state;1: SCK is high in idle state
	#define		CPHA    0x10		//SPI Clock Phase.0: Data sampled on first edge of SCK period;1: Data sampled on second edge of SCK period
	#define		SSDIS   0x08		//Pin Disable.0: Enable   pin;1: Disable   pin
	#define		LSBF    0x04 	  //LSB First.0: MSB first;1: LSB first
	//--------------------------------------------------------------------------------------------
	//SPCR:SPCON Bit[1:0], SPI Clock Rate.
	#define		SPCR_2  0x00	  //FSYSCLK/2
	#define		SPCR_4  0x01	  //FSYSCLK/4
	#define		SPCR_8  0x02	  //FSYSCLK/8
	#define		SPCR_16 0x03	  //FSYSCLK/16
/*
******************************************************************************************************
*/
sfr   SPSTA     = 0xBD;
	#define		SPSTA_Mask	~0xFC		//
	#define		SPIF    0x80		//SPI Interrupt Flag,This bit is set by H/W at the end of a data transfer,Cleared by H/W when interrupt is vectored into
	#define		WCOL    0x40 	  //Write Collision Interrupt Flag,This bit is set by H/W if write data to SPDAT when SPBSY is set.Write 0 to this bit or rewrite data to SPDAT when SPBYS is cleared will clear this flag.
	#define		MODF    0x20	  //Mode Fault Interrupt Flag,This bit is set by H/W when SSDIS is cleared and NSS pin is pulled low in Master mode.Write 0 to this bit will clear this flag.When this bit is set, the SPEN and MSTR in SPCON will be cleared by hardware
	#define		RCVOVF  0x10		//Receive Buffer Overrun Flag,This bit is set by H/W at the end of a data transfer and RCVBF is set.Write 0 to this bit or read SPDAT register will clear this flag.
	#define		RCVBF   0x08		//Receive Buffer Full Flag,This bit is set by H/W at the end of a data transfer.Write 0 to this bit or read SPDAT register will clear this flag.
	#define		SPBSY   0x04 	  //SPI Busy Flag (Read Only),This bit is set by H/W when a SPI transfer is progress.
/*
******************************************************************************************************
*/
sfr   SPDAT     = 0xBE;	//Bit[7:0],SPI Transmit and Receive Data
/*
******************************************************************************************************
*/
sfr   LVDCON     = 0xBF;	//Bit[3:0],16 level LVR select
	#define LVDM	0x80	
	//"0: VCC < LVDS  ==> LVDIF =1 and LVDO =1
	//1: VCC > LVDS ==> LVDIF =1 and LVDO =1"

	#define LVDO	0x40	//LVDO
	
	#define LVDDBS	0x20	//LVDO debounce
	//0: disable
	//1: enable
	
	#define	LVDPD	0x10
	//"0: Enable LVD
	//1: Disable LVD"	

	//--------------------------------------------------------------------------------------------
	//LVDS:LVDS Bit[3:0], LVD level Select 2.6V~4.4V 16階
/*
******************************************************************************************************
*/
sfr 	P5  			= 0xC0;	//Port5 Data
	sbit 		P5_7		= P5^7;
	sbit 		P5_6		= P5^6;
	sbit 		P5_5		= P5^5;
	sbit 		P5_4		= P5^4;
	sbit 		P5_3		= P5^3;
	sbit 		P5_2		= P5^2;
	sbit 		P5_1		= P5^1;
	sbit 		P5_0		= P5^0;
/*
******************************************************************************************************
*/
sfr 	TKPINSA0  		= 0xC1;
	//--------------------------------------------------------------------------------------------
	//TKPINSA0:TKPINSA0 Bit[7:0], "Touch Key TKA00~TKA07 Channel Select
								//0: Normal IO
								//1: Touch Key"

/*
******************************************************************************************************
*/
sfr 	TKPINSA1  		= 0xC2;
	//--------------------------------------------------------------------------------------------
	//TKPINSA1:TKPINSA1 Bit[7:0], "Touch Key TKA08~TKA15 Channel Select
								//0: Normal IO
								//1: Touch Key"

sfr 	TKPINSA2  		= 0xC3;
	//--------------------------------------------------------------------------------------------
	//TKPINSA2:TKPINSA2 Bit[7:0], "Touch Key TKA16~TKA20 Channel Select
								//0: Normal IO
								//1: Touch Key"

/*
******************************************************************************************************
*/
sfr 	TKPINSB0  		= 0xC4;
	//--------------------------------------------------------------------------------------------
	//TKPINSB0:TKPINSB0 Bit[7:0], "Touch Key TKB00~TKB07 Channel Select
								//0: Normal IO
								//1: Touch Key"


sfr 	ATKCHA0  		= 0xC5;
	//--------------------------------------------------------------------------------------------
	//ATKCHA0:ATKCHA0 Bit[7:0], "Auto Touch Key TK0~TK7 Channel Select
								//0: Disable auto scan
								//1: Enable auto scan"

sfr 	ATKCHA1  		= 0xC6;
	//--------------------------------------------------------------------------------------------
	//ATKCHA1:ATKCHA1 Bit[7:0], "Auto Touch Key TK8~TK15 Channel Select
								//0: Disable auto scan
								//1: Enable auto scan"

sfr 	ATKCHA2  		= 0xC7;
	//--------------------------------------------------------------------------------------------
	//ATKCHA2:ATKCHA2 Bit[7:0], "Auto Touch Key TK16~TK20 Channel Select
								//0: Disable auto scan
								//1: Enable auto scan"

/*
******************************************************************************************************
*/

sfr 	T2CON  		= 0xC8;
	//--------------------------------------------------------------------------------------------
	sbit 		TF2  		  = T2CON^7;		//定时器T2溢出中断标志。TF2必须由用户程序清“0”。当T2作为串口波特率发生器时，TF2不会被置“1”。
	sbit 		EXF2    	= T2CON^6;		//外部中断2标志。EXEN2为1时，当T2EX（P1.1）发生负跳变时置1中断标志DXF2，EXF2必须由用户程序清“0”。
	sbit 		RCLK    	= T2CON^5;		//串行接口的接收时钟选择标志。TCLK=1时，T2工作于mode 1 or 3。
	sbit 		TCLK 		  = T2CON^4;		//串行接口的发送时钟选择标志位。RCLK＝1时，T2工作于mode 1 or 3。
	sbit 		EXEN2   	= T2CON^3;		//外部中断2充许标志。
	sbit 		TR2     	= T2CON^2;		//Timer2运行控制。0：Timer2停止，1：Timer2运行
	sbit 		CT2N     	= T2CON^1;		//Timer2计数器/定时器选择位。C/T2=0时，为定时器模式。C/T2=1时，为计数器模式。
	sbit 		CPRL2N   	= T2CON^0;		//捕捉和常数自动再装入方式选择位。为1时工作于捕捉方式，为0时T2工作于常数自动再装入方式。当TCLK或RCLK为1时，CP/RL2被忽略，T2总是工作于常数自动再装入方式。
/*
******************************************************************************************************
*/
sfr		IAPWE_SFR	= 0xC9;	
	//"Write 4Ato enable one byte IAP write to ROM[FA00~FBFF]
	//Write 4Ch to enable one byte IAP write to ROM[FC00~FDFF] 
	//Write BAh to enable ERASE 512 byte of ROM[7A00~7BFF] , and wirte any data to ROM[FB2D]
	//Write BCh to enable ERASE 512 byte of ROM[7C00~7DFF] , and wirte any data to ROM[FD69]
	//other value to disable IAP write"
						
	#define	IAPWE	0x80	//IAPWE Enable/Erase Flag
							
	#define	IAPTO	0x40	//IAP Time-Out flag,? H/W auto clear when IAPWE =0

/*
******************************************************************************************************
*/

sfr 	RCP2L  	 = 0xCA;				//外部输入（P1.1）计数器/自动再装入模式时初值寄存器低八位
sfr 	RCP2H  	 = 0xCB;				//外部输入（P1.1）计数器/自动再装入模式时初值寄存器高八位
/*
******************************************************************************************************
*/
sfr 	TL2   		= 0xCC;			  	//Timer2 data low byte
sfr 	TH2   		= 0xCD;			    //Timer2 data high byte
/*
******************************************************************************************************
*/
sfr 	EXA2   		= 0xCE;	//Bit[7:0],extra ACC for 32/16 bit division operation
/*
******************************************************************************************************
*/
sfr 	EXA3   		= 0xCF;	//Bit[7:0],extra ACC for 32/16 bit division operation
/*
******************************************************************************************************
*/
sfr 	PSW   		= 0xD0;
	//--------------------------------------------------------------------------------------------
	sbit 		CY 		= PSW^7;		//进位标志,有进或借位,CY＝1;无进或借位,CY＝0
	sbit 		AC 		= PSW^6;		//半进位标志
	sbit 		F0 		= PSW^5;		//用户可以随便使用
	sbit 		RS1 	= PSW^4;		//工作寄存器区选择[RS1:RS0]
	sbit 		RS0 	= PSW^3;		//00--0区,01--1区,10--2区,11--3区
	sbit 		OV 		= PSW^2;		//运算结果按补码运算,有溢出,OV=1;无溢出,OV＝0
	sbit 		F1 		= PSW^1;		//用户自定义标志
	sbit 		P 		= PSW^0;		//用来表示ALU运算结果中二进制数位1的个数的奇偶性,若为奇数,则P=1,否则为0
/*
******************************************************************************************************
*/
sfr 	PWMDH 		= 0xD1;	
	//extra condition: write sequence:  pwmxprdl then pwmxprdh
	//extra condition: read sequence:  pwmxprdh then pwmxprdl
	/*Bit[7:0],"PWMIDX:
		0xh: PWM0DH access
		1xh: PWM1DH access
		2xh: PWM2DH access
		30h: PWM30DH access
		31h: PWM31DH access
		32h: PWM32DH access
		33h: PWM33DH access
		34h: PWM34DH access
		35h: PWM35DH access*/

/*
******************************************************************************************************
*/
sfr 	PWMDL 		= 0xD2;	
	/*Bit[7:0],"PWMIDX:
		0xh: PWM0DL access
		1xh: PWM1DL access
		2xh: PWM2DL access
		30h: PWM3DL access
		31h: PWM31DH access
		32h: PWM32DH access
		33h: PWM33DH access
		34h: PWM34DH access
		35h: PWM35DH access"*/


/*
******************************************************************************************************
*/
sfr 	UART2CON 		= 0xD5;	
	//UART2BRP:UART2CON,Bit[6:0],
	//"Define UART2 baud rate pre-scaler
	//UART2 BAUD rate = Fsys/32/UART2BRP"
	
/*
******************************************************************************************************
*/
sfr 	LVRCON 		= 0xD6;	
	//SXTGAIN:LVRCON,Bit[7:6],
	//SXT GAIN select
	//00: lowest
	//11: highest

	#define	LVRPD	0x10	//0: Enable LVR,1: Disable LVR
	
	//LVRS:LVRCON,Bit[2:0],
	//LVR level Select,2.2V~4V 8-level
	
/*
******************************************************************************************************
*/
sfr 	TKPINSB1 		= 0xD7;	
	//TKPINSB1:TKPINSB1,Bit[7:0],
		//"Touch Key TKB09~TKB15 Channel Select
		//0: Normal IO
		//1: Touch Key"

/*
******************************************************************************************************
*/
sfr		CLKCON		= 0xD8;
	sbit 		SCKTYPE = CLKCON^7;		//慢时钟类型选择，Slow Clock Type. 1=SXT, 0=SRC
	sbit 		FCKTYPE = CLKCON^6;		//Fast clock Type. 0=FRC, 1=FXT.
	sbit 		STPSCK  = CLKCON^5;		//Stop Slow Clock in PDOWN
	sbit 		STPPCK 	= CLKCON^4;		//1=进入IDL模式，Stop UART/Timer0/Timer1/Timer2 Clock in Idle mode
	sbit 		STPFCK 	= CLKCON^3;		//1=停止快时钟，0=快时钟工作
	sbit 		SELFCK 	= CLKCON^2;		//1=选择快时钟为系统时钟源，0=慢时钟为系统时钟源
	sbit 		CLKPSC_H 	= CLKCON^1;	
	sbit 		CLKPSC_L	= CLKCON^0;	
	//********************************************************************************************************
	#define  	SCKTYPE_SRC    	CLKCON=(CLKCON&0x7f)
	#define  	SCKTYPE_SXT    	CLKCON=(CLKCON&0x7f)|0x80
	#define		FCKTYPE_FRC		CLKCON=(CLKCON&0x9f)
	#define		FCKTYPE_FXT		CLKCON=(CLKCON&0x9f)|0x40
	#define		STPPCK_OPEN		CLKCON=(CLKCON&0xef)
	#define		STPPCK_OFF		CLKCON=(CLKCON&0xef)|0x10
	#define		STPFCK_OPEN		CLKCON=(CLKCON&0xf7)
	#define		STPFCK_OFF		CLKCON=(CLKCON&0xf7)|0x08
	#define		SELFCK_SLOW		CLKCON=(CLKCON&0xfb)
	#define		SELFCK_FAST		CLKCON=(CLKCON&0xfb)|0x04
	//--------------------------------------------------------------------------------------------
	//CLKPSC:CLKCON Bit[1:0],系统时钟预分频，SYSCLK Prescaler, 0:div16, 1:div4, 2:div2, 3:div1
	#define		CLKPSC_Mask							~0x03
	#define		CLKPSC_Div16		0x00
	#define		CLKPSC_Div4			0x01
	#define		CLKPSC_Div2			0x02
	#define		CLKPSC_Div1			0x03							
	#define		SYSCLOCK_DIV_16		CLKCON=(CLKCON&0xfc)
	#define		SYSCLOCK_DIV_4		CLKCON=(CLKCON&0xfc)|CLKPSC_Div4
	#define		SYSCLOCK_DIV_2		CLKCON=(CLKCON&0xfc)|CLKPSC_Div2
	#define		SYSCLOCK_DIV_1		CLKCON=(CLKCON&0xfc)|CLKPSC_Div1	
/*
******************************************************************************************************
*/
sfr 	PWMPRDH   		= 0xD9;		
	//"extra condition: write sequence:  PWMxPRDL then PWMxPRDH
	//extra condition: read sequence:  PWMxPRDH then PWRxPRDL"
	//PWMPRDH:PWMPRDH,Bit[7:0],
		//"PWMIDX:
		//0xh: PWM0PRDH access
		//1xh: PWM1PRDH access
		//2xh: PWM2PRDH access
		//3xh: PWM3PRDH access"

sfr 	PWMPRDL   		= 0xDA;
	//"extra condition: write sequence:  PWMxPRDL then PWMxPRDH
	//extra condition: read sequence:  PWMxPRDH then PWRxPRDL"
	//PWMPRDL:PWMPRDL,Bit[7:0],
		//"PWMIDX:
		//0xh: PWM0PRDH access
		//1xh: PWM1PRDH access
		//2xh: PWM2PRDH access
		//3xh: PWM3PRDH access"

/*
******************************************************************************************************
*/
sfr 	UART1CON   		= 0xDD;		
	//UART1BRP:UART1CON,Bit[6:0],
	//Define UART1 baud rate pre-scaler
	//UART1 BAUD rate = Fsys/32/UART1BRP"

/*
******************************************************************************************************
*/
sfr 	UART0CON   		= 0xDE;		
	#define	UART0BRS	0x80
	//UART0 baud rate source select
	//0:8051 default baud rate source select
	//1: UART0 BAUD rate select as UART0BRP
	
	//UART1BRP:UART1CON,Bit[6:0],
	//Define UART0 baud rate pre-scaler
	//UART0 BAUD rate = Fsys/32/UART0BRP
	
/*
******************************************************************************************************
*/
sfr 	TKPINSB2   		= 0xDF;		
	//TKPINSB2:TKPINSB2,Bit[4:0],
		//"Touch Key TKB16~TKB20 Channel Select
		//0: Normal IO
		//1: Touch Key"

/*
******************************************************************************************************
*/
sfr 	ACC   		= 0xE0;		//Accumulator

/*
******************************************************************************************************
*/
sfr 	MICON   	= 0xE1;
	#define	MIEN	0x80
	//Master IIC enable
	//0: disable
	//1: enable
	
	#define	MIACKO	0x40
	//When Master IIC receive data, send acknowledge to IIC Bus
	//0: ACK to slave device
	//1: NACK to slave device

	#define	MIIF	0x20
	//Master IIC Interrupt flag
	//1: Master IIC transmit or receive one byte complete
	//0: H/W write 0 to clear it

	#define	MIACKI	0x10
	//When Master IIC transmission, acknowledgement form IIC bus (read only)
	//0: ACK received
	//1: NACK received

	#define	MISTART	0x08
	//Master IIC Start bit 
	//1: start IIC bus transmit
	//period of MISTART=0 must longer than a IIC clock period

	#define	MISTOP	0x04
	//Master IIC Stop bit
	//1: send STOP signal to stop IIC bus
	
	//
	//MICR:MICON Bit[1:0]
	//00: Fsys/4    (ex. If Fsys=16MHz, IIC clock is  4M Hz)
	//01: Fsys/16   (ex. If Fsys=16MHz, IIC clock is  1M Hz)
	//10: Fsys/64   (ex. If Fsys=16MHz, IIC clock is 250K Hz)
	//11: Fsys/256  (ex. If Fsys=16MHz, IIC clock is 62.5K Hz)

//******************************************************************************************************
sfr 	MIDAT	= 0xE2;
	//Bit[7:0]
	//Master IIC data shift register
	//(W): After Start and before Stop condition, write this register will resume trasmission to IIC bus
	//(R): After Start and before Stop condition, read this register will resume receiving from IIC bus"

/*
******************************************************************************************************
*/
sfr 	EFTCON   = 0xE5;		//Accumulator
	#define		EFT2CS		0x80
	//"EFT2 Detector enable
	//0: Disable EFT2
	//1: Enable EFT2"

	#define		EFT1CS		0x40
	//"EFT1 Detector enable
	//0: Disable EFT1
	//1: Enable EFT1"

	//--------------------------------------------------------------------------------------------
	//EFT1S:EFTCON,Bit[5:4],EFT1 Detector sensitivity adjustment

	#define		EFTSLOW		0x08
	//"Force SYSCLK to SLOWCLK while EFT detected
	//0: Disable
	//1: Enable"

	#define		EFTWCPU		0x04
	//"CPU enter Wait state while EFT detected 
	//0: Disable
	//1: Enable"

	#define		EFTWOUT		0x02
	//"EFTWAIT output to pin 
	//0: P03 = normal I/O
	//1: P03 =  !EFTWAIT"

	#define		CKHLDE		0x01
	//"clock hold enable
	//0: Disable
	//1: Enable"

//******************************************************************************************************
sfr 	EXA		= 0xE6;//Bit[7:0],extra ACC for 16 bit mul/div operation

//******************************************************************************************************
sfr 	EXB		= 0xE7;//Bit[7:0],extra B for 16 bit mul/div operation

/*
******************************************************************************************************
*/
sfr 	P4	   = 0xE8;		//Port4 Data
	sbit 		P4_7		= P4^7;
	sbit 		P4_6		= P4^6;
	sbit 		P4_5		= P4^5;
	sbit 		P4_4		= P4^4;
	sbit 		P4_3		= P4^3;
	sbit 		P4_2		= P4^2;
	sbit 		P4_1		= P4^1;
	sbit 		P4_0		= P4^0;

//******************************************************************************************************
sfr 	SIADR	= 0xE9;
	//
	//SA:SIADR,Bit[7:1],
	//Write: I2C Slave address assigned
	//Read: address received at I2C Slave"

	#define	SIEN	0x01
	//Slave IIC enable
	//0: disable
	//1: enable"

//******************************************************************************************************
sfr 	SICON	= 0xEA;
	
	#define	MIIE	0x80
	//MIIE: I2C Master INT enable
	//0: Disable	1: Enable

	#define	TXDIE	0x40
	//TXDIE: I2C Slave data transmit INT enable
	//0: Disable	1: Enable

	#define	RCD2IE	0x20
	//RCD2IE: I2C Slave SIRCD2 receive INT enable
	//0: Disable	1: Enable

	#define	RCD1IE	0x10
	//RCD1IE: I2C Slave SIRCD1 receive INT enable
	//0: Disable	1: Enable

	#define	TXDF	0x04
	//I2C Slave data transmit INT flag, it will not transmit SITXD data and return NACK while 1
	//1: Set by H/W when Slave IIC SITXD transmit one byte complete
	//0: S/W write 0 to clear it

	#define	RCD2F	0x02
	//I2C Slave data received INT flag, it will not receive SITXD data and return NACK while 1
	//1: Set by H/W when Slave IIC SITXD receive one byte complete
	//0: S/W write 0 to clear it

	#define	RCD1F	0x01
	//I2C Slave data received INT flag, it will not receive SIRCD1 data and return NACK while 1
	//1: Set by H/W when Slave IIC RCVD1 receive one byte complete
	//0: S/W write 0 to clear it

//******************************************************************************************************
sfr 	SIRCD1 		= 0xEB;	//Bit[7:0],Slave IIC data receive register1

//******************************************************************************************************
sfr 	SITXRCD2	= 0xEC;	
	//Bit[7:0],
	//Write: Slave IIC data transmit register (data-out)
	//Read: Slave IIC data receive register2 (data-in)

//******************************************************************************************************
sfr 	BOOTV	= 0xEE;	
	#define	RSTV	0x04
	//RESET VECTOR
	//1: Reset vector = 0xE800/0xE000 (仅BOOT5K/7K使能有效)
	//0: Reset vector = 0x0000 

	//BOOTVR:BOOTV,Bit[1:0]
	//CPU read only; POR load from CFG.BOOTV
	//11: BOOT 5K, Reset 0xE800
	//10: BOOT 7K, Reset 0xE000
	//01: x
	//00: APP ROM, Reset 0x0000

//******************************************************************************************************
sfr 	PWRCON	= 0xEF;	

	#define		IAPINFO0	0x20
	//0: IAP INFO0 disable
	//1: IAP  INFO0 enable

	#define		AVPULL		0x10
	//0: disable
	//1: auto turn-on VPULL when SLOW to FAST "

	#define		Warmtime	0x08
	//0: 128 Clock
	//1: 64 Clock

	#define		ENVPULL		0x04
	//1: force VPULL enable

	#define		PWRIDLE		0x02
	//0: VDD = LDO @  IDLE mode
	//1: VDD = VPULL @  IDLE mode"

	#define		PWRSLOW		0x01
	//0: VDD = LDO @ SLOW modle
	//1: VDD = VPULL @ SLOW mode"

//******************************************************************************************************
sfr 	B   		= 0xF0;		//B register
//******************************************************************************************************
sfr 	CRCDL   		= 0xF1;
	//--------------------------------------------------------------------------------------------
	//CRCDL:CRCDL Bit[7:0],CRC16 Data 7~0
//******************************************************************************************************
sfr 	CRCDH   		= 0xF2;
	//--------------------------------------------------------------------------------------------
	//CRCDH:CRCDH Bit[7:0],CRC16 Data 15~8
//******************************************************************************************************
sfr 	CRCIN   		= 0xF3;
	//--------------------------------------------------------------------------------------------
	//CRCIN:CRCIN Bit[7:0],CRC16 input
//******************************************************************************************************
sfr 	TESTMODE 		= 0xF4;
	
	#define	LEDOM		0x80
	//1: force LED COM/SEG as output @xfetch mode
	
	#define	TKRAM		0x40
	//1: tkram can be write by cpu

	#define	FTKTEST		0x20	
	//0: normal mode
	//1: TCK = RCK
    //RCK output to IO (P15/P17)
    //TCK output to IO (P14/P16)

	#define	LEDDRV	0x10
	//0: LED IO normal drive, normal sink
	//1: LED IO weak drive, high sink (P1 120mA)

	#define	AEDCTRL	0x08
	//0: CCLK delay 8ns to ROM AE
	//1: CCLK delay 6ns to ROM AE"

	#define	LDOPD	0x04
	//extra contition password IAPWE == F4h
	//0: Enable LDO
	//1: Disable LDO"

	#define	AEOUT	0x02	//1: AE OUTPUT to P41

	#define	PORPD	0x01	//0: Enable POR, 1: Disable POR"


sfr 	CFGBG   		= 0xF5;
	//--------------------------------------------------------------------------------------------
	//BGTRIM:CFGBG Bit[4:0],VBG trimming value
	
sfr 	CFGWL   		= 0xF6;
	//--------------------------------------------------------------------------------------------
	//FRCF:CFGWL Bit[6:0],FRC freq
	
/*
******************************************************************************************************
*/	
sfr 	AUX2		= 0xF7;
	//--------------------------------------------------------------------------------------------
	//WDTE:AUX2 Bit[7:6],WDT Control
	//#define 	WDT_EN		         	0xC0		
	//#define 	WDT_ENFS_DISILDE		0x80		
	//#define 	WDT_DIS         		0x00		
	
	#define  	WDT_IS_OPEN                         AUX2=(AUX2&0x3f)|0xC0	//11：WDT Always Enable
	#define   	WDT_RUN_FAST_DIS_ILDE               AUX2=(AUX2&0x3f)|0x80	//10: WDT Enable in Fast/Slow, Disable in Idle/Stop mode
	#define  	WDT_IS_STOP                         AUX2=AUX2&0x3f  		//0X: WDT Disable

	#define		PWRSAV		0x20
	#define		PDOWN_LVD_ENABLE	AUX2=(AUX2&0xdf)
	#define		PDOWN_LVD_DISABLE	AUX2=(AUX2&0xdf)|PWRSAV
	
	#define		VBGOUT		0x10	//1: VBG output to P3.2,   0: P32 as normal IO
									//extra condition VBGEN =1
	#define		VBG_OUTPUT	AUX2=(AUX2&0xef)|VBGOUT
	#define		VBG_NORMAL	AUX2=(AUX2&0xef)
	
	#define		DIV32		0x08	
	//0: 16/16 division operation
	//1: 32/16 division operation"

	//
	//IAPTE:AUX2 Bit[2:1],
	//IAP TIMEOUT Enable  00:Disable  01: 0.8mS   10:  3.2mS  11:6.4mS
	
	#define		MULDIV16	0x01
	//0: instruction mul/div as 8*8, 8/8 operation
	//1: instruction mul/div as 16*16, 16/16 or 32/16 operation"

/*
******************************************************************************************************
*/	
sfr 	AUX1   		= 0xF8;
	//--------------------------------------------------------------------------------------------
    sbit 		CLRWDT		= AUX1^7;	//Clear WatchDog, HW auto clear
    #define     CLR_WDT		CLRWDT=1                   //设置以清除看门狗定时器,H/W自动在一个时钟周期清除它     	
	sbit 		CLRTM3		= AUX1^6;//清零TM3也是启动TM3
    sbit 		TKSOCA		= AUX1^5;//触摸键开始，结束时自动清零
									 //Touch Key A Start of Conversion
									 //HW clear while end of conversion while TKRERUN=0

    sbit 		ADSOC		= AUX1^4;//ADC开始，结束时硬件自动清零
    //sbit 		LVRPD		= AUX1^3;//0: Enable LVR
									 //1: Disable LVR
    sbit 		TKSOCB		= AUX1^2;//Touch Key B Start of Conversion
									 //HW clear while end of conversion while TKRERUN=0"

    sbit 		T1SEL		= AUX1^1;//Timer1 counter mode input. 0: P3.5 pin, 1: SLOWCLK/16
    sbit 		DPSEL		= AUX1^0;//0表选择DPTR;1表选择DPTR1

/*
******************************************************************************************************
*/	
sfr 	ICECON 		= 0xF9;
	#define 	HWBRK		      0x80		//H/W Break
	#define 	RETROREQ          0x40		//Retro-OPCode Request; HW set at SWBRK, clear at NMI return
	#define 	TXRDY		      0x20		//Transmitt Ready
	#define 	CMDRDY	          0x10		//Ready for Command
	#define 	XFIRCK	          0x08		//Swich to FIRC 4MHZ
	#define 	WRFAIL	          0x01		//EEPROM Write Fail
/*
******************************************************************************************************
*/	
//sfr 	RETROCODE 		= 0xFA;	//ICE Mode Retro-OPCode
/*
******************************************************************************************************
*/	
sfr 	ICERCD 		= 0xFB;	//ICE Mode Receive Data
/*
******************************************************************************************************
*/	
sfr 	ICECMDL		= 0xFC;	//ICE Mode Command Low Byte
/*
******************************************************************************************************
*/	
sfr 	ICECMDH		= 0xFD;	//Bit[3:0],ICE Mode Command High Byte

/*
******************************************************************************************************
*/	
sfr 	ICETXD		= 0xFE;	//ICE Mode Transmit Data
/*
******************************************************************************************************
*/	
sfr 	INDSFR		= 0xFF;	//SFR direct Adr replaced by ICECMDL in NMI "mov dir,a" & "mov a,dir"
/*
******************************************************************************************************
*/	

#define	 All_Interrupt_Enable()		EA = 1		//全局中断使能
#define	 All_Interrupt_Disable()	EA = 0		//全局中断关闭
/*
******************************************************************************************************
*/
#define	 PowerDown()			PCON = PCON | PD	//进入掉电模式
#define	 PowerIdel()			PCON = PCON | IDL	//进入IDEL模式
/*
******************************************************************************************************
*/
#endif
/*
******************************************************************************************************
*/
