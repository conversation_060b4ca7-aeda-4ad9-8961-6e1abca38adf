#include "main.h"

uint8_t t1ms_cnt = 0;
uint8_t t10ms_cnt = 0;
uint8_t scan_key_time = 0;
uint8_t countdown_time_ended = 0;
uint8_t first_power_on = 0;
uint8_t led_light = 0;
uint16_t led_light_num = 0;
uint8_t key_led_light = 0;
uint8_t ambient_light = 0;
uint8_t led_light_sctime = 0;

uint8_t snooze_time_ended = 0; // 贪睡时间结束标志

uint8_t halt_mode_flag = 0;  // 进入halt模式标志 1:进入halt模式 0:退出halt模式
uint16_t halt_mode_time = 0; // halt模式时间

void timer0_init()
{
    TMOD = (TMOD & ~TMOD_TMOD0_MASK) | 1 << TMOD_TMOD0_POS; // Timer0模式选择  16位定时器

    TH0 = (TIME_RELOAD) / 256; // 100US
    TL0 = (TIME_RELOAD) % 256;

    TR0 = 1; // TIMER0 运行
    ET0 = 1;
}

void tim3_init(void)
{
    SET_REG_BITS(OPTION, TM3CKS, 0); // Timer3慢时钟 (SXT)   32.768K
    SET_REG_BITS(OPTION, TM3PSC, 1); // 中断率控制选择16384慢时钟周期
    SET_REG_BITS(INTE1, TM3IE, 1);   // 允许Timer3 中断使能控制

    // LVRCON = 0x50;
    LVRCON |= 0x40;
    SCKTYPE = 1;                        // SXT
    PORTIDX = PORT0;                    // 初始化晶振引脚
    PINMOD32 = PINMOD32 & ~0xff | 0x33; // P03  P02
}

void timer0_int() interrupt 1
{
    TH0 = (TIME_RELOAD) / 256; // 100US
    TL0 = (TIME_RELOAD) % 256;

    t1ms_cnt++;
    scan_key_time++;
    led_light_sctime++;
    breath_led_exe();

    if (led_light_sctime > 3)
    {
        led_light_sctime = 0;
    }

    if (time_state == STATE_POWER_OFF)
    {
        LED_WHITE = 0;
    }
    else if (time_state == STATE_SLEEP)
    {
        // 睡眠状态下，根据进入睡眠前的环境灯状态决定LED_WHITE的状态
        if (ambient_light_before_sleep == KEY_LED_LEVEL_0)
        {
            LED_WHITE = 1; // 如果进入睡眠前是关的，保持关闭
        }
        else
        {
            // 如果闹钟响起，让氛围灯呼吸而不是常亮
            if (alarm_state)
            {
                // 闹钟状态下由breath_led_exe控制呼吸效果，这里不控制LED_WHITE
            }
            else
            {
                // 如果进入睡眠前是开的，保持原来的PWM控制
                if (led_light_sctime < ambient_light_before_sleep)
                {
                    LED_WHITE = 0;
                }
                else
                {
                    LED_WHITE = 1;
                }
            }
        }
    }
    else if (ble_pair_flag) // 蓝牙配对模式
    {
        // MODE_BT_PAIR模式下，LED_WHITE由breath_led_exe()中的呼吸效果控制
        // 这里不控制LED_WHITE，避免与呼吸效果冲突
    }
    else if (alarm_state)
    {
        // 处于闹铃状态时，由breath_led_exe控制呼吸效果
        // 不在这里控制LED_WHITE
    }
    else
    {
        if (led_light_sctime < ambient_light)
        {
            LED_WHITE = 0;
        }
        else
        {
            LED_WHITE = 1;
        }
    }

    // 数码管显示控制 - 使用高频扫描避免闪烁
    if (display_led_breath_on_flag || display_led_breath_off_flag)
    {
        // 呼吸效果期间，调整数码管亮度而不是开关
        uint8_t display_brightness = key_led_light;

        if (display_led_breath_on_flag)
        {
            // 渐亮：根据呼吸进度调整亮度 (0-100 映射到 0-key_led_light)
            display_brightness = (key_led_light * breath_display_on_duty) / 100;
            if (display_brightness == 0 && breath_display_on_duty > 0)
                display_brightness = 1; // 确保有最小亮度
        }
        else if (display_led_breath_off_flag)
        {
            // 渐灭：根据呼吸进度调整亮度 (100-0 映射到 key_led_light-0)
            display_brightness = (key_led_light * breath_display_off_duty) / 100;
        }

        // 使用调整后的亮度进行高频扫描，保持400us周期
        if (led_light_sctime < display_brightness)
        {
            led_task(); // 正常显示数码管
        }
        else
        {
            all_led_seg_off();
        }
    }
    else
    {
        // 正常模式下的数码管控制
        if (led_light_sctime < key_led_light)
        {
            led_task();
        }
        else
        {
            all_led_seg_off();
        }
    }

    if (t1ms_cnt >= 10) // 1ms
    {
        t1ms_cnt = 0;
        scan_switch_time++;
        scan_flash_time++;
        t10ms_cnt++;
        halt_mode_time++;
        if (t10ms_cnt >= 100) // 100ms
        {
            t10ms_cnt = 0;
            led_state_scan_time++;
        }
    }

    // 防溢出
    if (scan_key_time >= 200)
        scan_key_time = 0;

    if (scan_switch_time >= 200)
        scan_switch_time = 0;

    // 防止halt_mode_time溢出
    if (halt_mode_time >= 60000) // 60秒后重置，避免溢出
        halt_mode_time = 0;
}

void tim3() interrupt 7
{
    if (first_power_on)
        return;
    // 实时时间
    rtc.second++;
#if TEST_MODE_ENABLE
    if (rtc.second >= 4)
#else
    if (rtc.second >= 120) // 1min
#endif
    {
        rtc.second = 0;
        rtc.minute++;
        if (rtc.minute >= 60) // 1h
        {
            rtc.minute = 0;
            rtc.hour++;
            if (rtc.hour >= 24)
            {
                rtc.hour = 0;
            }
        }

        // 倒计时
        if (count_down_flag)
        {
            if (rtc.countdown_time > 0)
            {
                rtc.countdown_time--;
            }

            if (rtc.countdown_time == 0)
            {
                countdown_time_ended = 1;
            }
        }

        // 贪睡
        if (snooze_state)
        {
            if (rtc.snooze_time > 0)
            {
                rtc.snooze_time--;
            }

            if (rtc.snooze_time == 0)
            {
                snooze_time_ended = 1;
            }
        }

        // 闹钟自动关闭计时
        if (alarm_state && alarm_auto_close_time > 0)
        {
            alarm_auto_close_time--;
            if (alarm_auto_close_time == 0)
            {
                // 3分钟时间到，设置自动关闭标志
                alarm_auto_close_flag = 1;
            }
        }
    }
}

void port_irq() interrupt 8
{
    if (INTFLG & PCIF)
    {
        INTFLG = INTFLG & ~PCIF;
        if (KEY_INT)
        {
            scan_key_enable = 1;
        }
        else
        {
            scan_key_enable = 0;
        }

        if (VCC_DETECT && time_state == STATE_POWER_OFF)
        {
            vcc_detect_flag = 1;
            halt_mode_flag = 0;
        }
        else if (!VCC_DETECT)
        {
            vcc_detect_flag = 0;
            halt_mode_flag = 1;
        }
    }
}
