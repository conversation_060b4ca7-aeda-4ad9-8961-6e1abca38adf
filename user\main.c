#include "main.h"

rtc_t rtc;
vol_t vol;
enum e_state time_state;
enum e_music_mode music_mode;

void wdg_init()
{
    SET_REG_BITS(OPTION, WDTPSC, 0); // 240MS
    SET_REG_BITS(AUX2, WDTE, 2);     // 看门狗在快钟和慢钟模式下使能，空闲/停止/暂停模式下禁止
    SET_REG_BITS(AUX1, CLRWDT, 0);
    CLR_WDT; // 设置以清除看门狗定时器
}

void bsp_init()
{
    timer0_init();
    tim3_init();
    iic_init();
    led_init();
    key_hw_init();
    switch_hw_init();
    uart_init();
    wdg_init();
}

void user_init()
{
    rtc.hour = DEFAULT_HOUR;
    rtc.minute = DEFAULT_MINUTE;
    rtc.second = 0;
    rtc.alarm_hour = ALARM_NOT_SET_HOUR;
    rtc.alarm_minute = ALARM_NOT_SET_MINUTE;
    rtc.countdown_time = DEFAULT_COUNTDOWN_TIME;
    rtc.snooze_time = DEFAULT_SNOOZE_TIME;
    countdown_last_value = rtc.countdown_time;

    vol.idl_vol = DEFAULT_IDL_VOL;
    vol.bt_vol = DEFAULT_BT_VOL;
    vol.aux_vol = DEFAULT_AUX_VOL;
    vol.ambient_vol = DEFAULT_AMBIENT_VOL;

    time_state = STATE_POWER_OFF;
    music_mode = MODE_IDE;
    power_flag = 0;
    countdown_time_ended = 0;
    alarm_set_flag = 0;
    first_power_on = 1;
    key_led_light = KEY_LED_LEVEL_4;
    ambient_light = KEY_LED_LEVEL_4;
    count_down_flag = 0;
    halt_mode_flag = 1;

    // 初始化闹钟和贪睡相关标志位
    alarm_state = 0;
    snooze_state = 0;
    alarm_triggered_today = 0;
    snooze_time_ended = 0;
    alarm_flag = 0;            // 初始化闹钟开关标志，将在switch_task中根据实际开关状态更新
    alarm_auto_close_time = 0; // 初始化闹钟自动关闭计时器
    alarm_auto_close_flag = 0; // 初始化闹钟自动关闭标志

    // 初始化蓝牙相关标志位
    ble_pair_flag = 0;
    ble_reconnect_flag = 0;

    key_state_init();
    switch_state_init();
}

int main()
{
    bsp_clock_init();
    bsp_init();
    user_init();
    EA = 1;

    // 检查插电开机：如果检测到电源且处于关机状态，自动开机
    if (VCC_DETECT && time_state == STATE_POWER_OFF)
    {
        vcc_detect_flag = 1;
        halt_mode_flag = 0;
        power_on_exe(); // 直接执行开机

        // 发送开机信号
        key_data[0] = 0x01;
        key_data[1] = 0x0a; // 开机信号
        uart0_send(key_data, 4);
        key_data[0] = 0;
        key_data[1] = 0;
    }

    while (1)
    {
        CLRWDT = 1; // 喂狗
        key_task();
        switch_task();
        led_flash_task();
        uart_data_handle();
        led_state_task();
        alarm_task();
        enter_halt_mode();
    }
}
