C51 COMPILER V9.60.7.0   TIME                                                              08/01/2025 16:40:40 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE TIME
OBJECT MODULE PLACED IN .\Objects\time.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\time.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hardwar
                    -e) DEBUG OBJECTEXTEND PRINT(.\Listings\time.lst) TABS(2) OBJECT(.\Objects\time.obj)

line level    source

   1          #include "main.h"
   2          
   3          void hour_inc(unsigned char *hour)
   4          {
   5   1          (*hour)++;
   6   1          if (*hour >= 24)
   7   1          {
   8   2              *hour = 0;
   9   2          }
  10   1      }
  11          
  12          void hour_dec(unsigned char *hour)
  13          {
  14   1          if (*hour == 0)
  15   1          {
  16   2              *hour = 23;
  17   2          }
  18   1          else
  19   1          {
  20   2              (*hour)--;
  21   2          }
  22   1      }
  23          
  24          void minute_inc(unsigned char *minute)
  25          {
  26   1          (*minute)++;
  27   1          if (*minute >= 60)
  28   1          {
  29   2              *minute = 0;
  30   2          }
  31   1      }
  32          
  33          void minute_dec(unsigned char *minute)
  34          {
  35   1          if (*minute == 0)
  36   1          {
  37   2              *minute = 59;
  38   2          }
  39   1          else
  40   1          {
  41   2              (*minute)--;
  42   2          }
  43   1      }
  44          
  45          void change_countdown_time_inc()
  46          {
  47   1          if (rtc.countdown_time == 0)
  48   1          {
  49   2              rtc.countdown_time = 15;
  50   2          }
  51   1          else if (rtc.countdown_time == 15)
  52   1          {
  53   2              rtc.countdown_time = 30;
  54   2          }
C51 COMPILER V9.60.7.0   TIME                                                              08/01/2025 16:40:40 PAGE 2   

  55   1          else if (rtc.countdown_time == 30)
  56   1          {
  57   2              rtc.countdown_time = 60;
  58   2          }
  59   1          else if (rtc.countdown_time == 60)
  60   1          {
  61   2              rtc.countdown_time = 0;
  62   2          }
  63   1          else
  64   1          {
  65   2              // 如果值不在预期范围内，修正为最接近的有效值
  66   2              if (rtc.countdown_time < 8) // 0-7 -> 0
  67   2              {
  68   3                  rtc.countdown_time = 0;
  69   3              }
  70   2              else if (rtc.countdown_time < 23) // 8-22 -> 15
  71   2              {
  72   3                  rtc.countdown_time = 15;
  73   3              }
  74   2              else if (rtc.countdown_time < 45) // 23-44 -> 30
  75   2              {
  76   3                  rtc.countdown_time = 30;
  77   3              }
  78   2              else // 45+ -> 60
  79   2              {
  80   3                  rtc.countdown_time = 60;
  81   3              }
  82   2          }
  83   1      }
  84          
  85          void change_countdown_time_dec()
  86          {
  87   1          if (rtc.countdown_time == 15)
  88   1          {
  89   2              rtc.countdown_time = 0;
  90   2          }
  91   1          else if (rtc.countdown_time == 30)
  92   1          {
  93   2              rtc.countdown_time = 15;
  94   2          }
  95   1          else if (rtc.countdown_time == 60)
  96   1          {
  97   2              rtc.countdown_time = 30;
  98   2          }
  99   1          else if (rtc.countdown_time == 0)
 100   1          {
 101   2              rtc.countdown_time = 60;
 102   2          }
 103   1          else
 104   1          {
 105   2              // 如果值不在预期范围内，修正为最接近的有效值
 106   2              if (rtc.countdown_time < 8) // 0-7 -> 0
 107   2              {
 108   3                  rtc.countdown_time = 0;
 109   3              }
 110   2              else if (rtc.countdown_time < 23) // 8-22 -> 15
 111   2              {
 112   3                  rtc.countdown_time = 15;
 113   3              }
 114   2              else if (rtc.countdown_time < 45) // 23-44 -> 30
 115   2              {
 116   3                  rtc.countdown_time = 30;
C51 COMPILER V9.60.7.0   TIME                                                              08/01/2025 16:40:40 PAGE 3   

 117   3              }
 118   2              else // 45+ -> 60
 119   2              {
 120   3                  rtc.countdown_time = 60;
 121   3              }
 122   2          }
 123   1      }
 124          
 125          void change_snooze_time()
 126          {
 127   1          if (rtc.snooze_time == 5)
 128   1          {
 129   2              rtc.snooze_time = 15;
 130   2          }
 131   1          else if (rtc.snooze_time == 15)
 132   1          {
 133   2              rtc.snooze_time = 20;
 134   2          }
 135   1          else if (rtc.snooze_time == 20)
 136   1          {
 137   2              rtc.snooze_time = 5;
 138   2          }
 139   1          else
 140   1          {
 141   2              // 如果值不在预期范围内，修正为最接近的有效值
 142   2              if (rtc.snooze_time < 10) // 0-9 -> 5
 143   2              {
 144   3                  rtc.snooze_time = 5;
 145   3              }
 146   2              else if (rtc.snooze_time < 18) // 10-17 -> 15
 147   2              {
 148   3                  rtc.snooze_time = 15;
 149   3              }
 150   2              else // 18+ -> 20
 151   2              {
 152   3                  rtc.snooze_time = 20;
 153   3              }
 154   2          }
 155   1      
 156   1          // 记录当前设置的贪睡时间，供下次使用
 157   1          last_snooze_time = rtc.snooze_time;
 158   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    284    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
