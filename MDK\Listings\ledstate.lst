C51 COMPILER V9.60.7.0   LEDSTATE                                                          07/26/2025 11:36:02 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE LEDSTATE
OBJECT MODULE PLACED IN .\Objects\ledstate.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\ledstate.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\har
                    -dware) DEBUG OBJECTEXTEND PRINT(.\Listings\ledstate.lst) TABS(2) OBJECT(.\Objects\ledstate.obj)

line level    source

   1          #include "main.h"
   2          
   3          uint8_t led_state_scan_time = 0;
   4          uint8_t ble_exit_cnt = 0;
   5          uint8_t ble_reconnect_exit_cnt = 0;
   6          uint8_t led_bt_pair_time = 0;
   7          
   8          void led_state_task()
   9          {
  10   1          if (time_state == STATE_POWER_OFF)
  11   1              return;
  12   1      
  13   1          if (led_state_scan_time >= 20) // 100*20ms=2s
  14   1          {
  15   2              led_state_scan_time = 0;
  16   2              if (music_mode == MODE_BT_PAIR)
  17   2              {
  18   3                  ble_pair_flag = 1;
  19   3                  ble_exit_cnt++;
  20   3                  if (ble_exit_cnt > 90) // 三分钟后退出
  21   3                  {
  22   4                      ble_pair_flag = 0;
  23   4                      ble_exit_cnt = 0;
  24   4                      music_mode = MODE_IDE;
  25   4                      time_state = STATE_REAL_TIME;
  26   4      
  27   4                      // 发送退出指令
  28   4                      key_data[0] = 0x01;
  29   4                      key_data[1] = 0x10;
  30   4                      uart0_send(key_data, 4);
  31   4                      key_data[0] = 0;
  32   4                      key_data[1] = 0;
  33   4                  }
  34   3              }
  35   2              else
  36   2              {
  37   3                  ble_exit_cnt = 0;
  38   3              }
  39   2      
  40   2              if (music_mode == MODE_BT_RECONNECT)
  41   2              {
  42   3                  ble_reconnect_exit_cnt++;
  43   3                  ble_reconnect_flag = 1;
  44   3                  if (ble_reconnect_exit_cnt > 15) // 30s无连接进入配对
  45   3                  {
  46   4                      ble_reconnect_exit_cnt = 0;
  47   4                      ble_reconnect_flag = 0;
  48   4                      ble_pair_flag = 1;
  49   4      
  50   4                      music_mode = MODE_BT_PAIR;
  51   4                      time_state = STATE_BT_NUM;
  52   4      
  53   4                      // 设置LED_WHITE呼吸效果的最大亮度为当前环境灯亮度
  54   4                      set_white_duty_config_by_ambient_light();
C51 COMPILER V9.60.7.0   LEDSTATE                                                          07/26/2025 11:36:02 PAGE 2   

  55   4      
  56   4                      key_data[0] = 0x01;
  57   4                      key_data[1] = 0x07;
  58   4                      uart0_send(key_data, 4);
  59   4                      key_data[0] = 0;
  60   4                      key_data[1] = 0;
  61   4                  }
  62   3              }
  63   2              else
  64   2              {
  65   3                  ble_reconnect_exit_cnt = 0;
  66   3              }
  67   2          }
  68   1      }
  69          
  70          void show_led_state()
  71          {
  72   1          if (time_state == STATE_POWER_OFF || time_state == STATE_SLEEP)
  73   1          {
  74   2              return;
  75   2          }
  76   1          else
  77   1          {
  78   2              if (pwm_led_on_flag || pwm_led_off_flag)
  79   2                  return;
  80   2      
  81   2              // 设置倒计时状态下的LED控制
  82   2              if (time_state == STATE_SET_COUNT_DOWN)
  83   2              {
  84   3                  // KEY_LED_TIMER 闪烁由 set_timer_flash() 函数控制
  85   3                  // KEY_LED_VOLUME_DOWN 和 KEY_LED_VOLUME_UP 常亮
  86   3                  KEY_LED_VOLUME_DOWN = 0;
  87   3                  KEY_LED_VOLUME_UP = 0;
  88   3      
  89   3                  // 其他LED熄灭
  90   3                  KEY_LED_POWER = 1;
  91   3                  KEY_LED_AUX = 1;
  92   3                  KEY_LED_BT = 1;
  93   3                  KEY_LED_AMB = 1;
  94   3                  KEY_LED_MUSIC = 1;
  95   3                  KEY_LED_PP = 1;
  96   3                  LED_SNOOZE = 1;
  97   3              }
  98   2              // 设置闹钟状态下的LED控制
  99   2              else if (time_state == STATE_SET_ALARM_HOUR || time_state == STATE_SET_ALARM_MINUTE)
 100   2              {
 101   3                  // KEY_LED_TIMER, KEY_LED_VOLUME_DOWN, KEY_LED_VOLUME_UP, KEY_LED_PP 常亮
 102   3                  KEY_LED_TIMER = 0;
 103   3                  KEY_LED_VOLUME_DOWN = 0;
 104   3                  KEY_LED_VOLUME_UP = 0;
 105   3                  KEY_LED_PP = 0;
 106   3      
 107   3                  // 其他LED熄灭
 108   3                  KEY_LED_POWER = 1;
 109   3                  KEY_LED_AUX = 1;
 110   3                  KEY_LED_BT = 1;
 111   3                  KEY_LED_AMB = 1;
 112   3                  KEY_LED_MUSIC = 1;
 113   3                  LED_SNOOZE = 1;
 114   3              }
 115   2              else
 116   2              {
C51 COMPILER V9.60.7.0   LEDSTATE                                                          07/26/2025 11:36:02 PAGE 3   

 117   3                  // 正常模式下，除了snooze灯之外，其他所有按键LED都常亮
 118   3      
 119   3                  // BT LED控制 - 特殊处理配对模式的闪烁
 120   3                  if (music_mode == MODE_BT_PAIR)
 121   3                  {
 122   4                      // BT配对模式下，KEY_LED_BT由ble_flash()函数控制闪烁
 123   4                      // 这里不设置，让闪烁函数控制
 124   4                  }
 125   3                  else
 126   3                  {
 127   4                      KEY_LED_BT = 0; // 正常模式下常亮
 128   4                  }
 129   3      
 130   3                  // TIMER LED控制 - 特殊处理设置模式的闪烁
 131   3                  if (time_state == STATE_SET_COUNT_DOWN)
 132   3                  {
 133   4                      // 设置倒计时模式下，KEY_LED_TIMER由set_timer_flash()函数控制闪烁
 134   4                      // 这里不设置，让闪烁函数控制
 135   4                  }
 136   3                  else
 137   3                  {
 138   4                      KEY_LED_TIMER = 0; // 正常模式下常亮
 139   4                  }
 140   3      
 141   3                  // 其他LED在正常模式下都常亮
 142   3                  KEY_LED_POWER = 0;
 143   3                  KEY_LED_VOLUME_DOWN = 0;
 144   3                  KEY_LED_VOLUME_UP = 0;
 145   3                  KEY_LED_AUX = 0;
 146   3                  KEY_LED_AMB = 0;
 147   3                  KEY_LED_MUSIC = 0;
 148   3                  KEY_LED_PP = 0;
 149   3      
 150   3                  // 只有snooze灯熄灭
 151   3                  LED_SNOOZE = 1;
 152   3              }
 153   2          }
 154   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    306    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =      4    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
