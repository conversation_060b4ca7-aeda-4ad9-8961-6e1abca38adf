#include "main.h"

// p3_4 scl
// p3_5 sda

// p3_0 scl
// p3_1 sda

void iic_init(void)
{
    SET_REG(PORTIDX, 3);
    SET_REG_BITS(PINMOD54, PINMOD4, PIN_MODE_OD_IPU); // P34
    SET_REG_BITS(PINMOD54, PINMOD5, PIN_MODE_OD_IPU); // P35
    P3_4 = 1;
    P3_5 = 1;
}

void iic_delay(void)
{
    uint8_t i;
    i = 0;
    while (i--)
        ;
}

void iic_start(void)
{
    SetSDA1(1);
    iic_delay();
    SetSCL1(1);
    iic_delay();
    SetSDA1(0);
    iic_delay();
    SetSCL1(0);
}

void iic_stop(void)
{
    SetSDA1(0);
    iic_delay();
    SetSCL1(1);
    iic_delay();
    SetSDA1(1);
}

void iic_ack(void)
{
    SetSDA1(0);
    iic_delay();
    SetSCL1(1);
    iic_delay();
    SetSCL1(0);
    iic_delay();
}

void iic_nack(void)
{
    SetSDA1(1);
    iic_delay();
    SetSCL1(1);
    iic_delay();
    SetSCL1(0);
}

unsigned char iic_recack(void)
{
    unsigned char AckBit;
    SetSDA1(1);
    SetSCL1(1);
    iic_delay();
    AckBit = SDA1;
    SetSCL1(0);
    return AckBit;
}

void iic_write_byte(unsigned char iic_data)
{
    uint8_t i;
    for (i = 0; i < 8; i++)
    {
        SetSCL1(0);
        iic_data <<= 1;
        if (CY)
            SetSDA1(1);
        else
            SetSDA1(0);
        iic_delay();
        SetSCL1(1);
        iic_delay();
    }
    SetSCL1(0);
}

unsigned char iic_read_byte(void)
{
    uint8_t i;
    uint8_t iic_data;
    SetSCL1(0);
    SetSDA1(1);
    for (i = 0; i < 8; i++)
    {
        SetSCL1(1);
        iic_delay();
        iic_data <<= 1;
        if (SDA1)
            iic_data |= 0x01;
        SetSCL1(0);
        iic_delay();
    }
    SetSDA1(1);
    return iic_data;
}
