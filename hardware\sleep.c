#include "main.h"

unsigned char test_buf[3] = {0};

void gpio_seelp_init(void)
{
    // GPIO0
    SET_REG(PORTIDX, 0);
    PINMOD10 = 0x22;
    PINMOD32 = 0x33;
    PINMOD54 = 0x22;
    PINMOD76 = 0x22;
    P0 = 0x0C;

    // GPIO1
    SET_REG(PORTIDX, 1);
    PINMOD10 = 0x29;
    PINMOD32 = 0x22;
    PINMOD54 = 0x22;
    PINMOD76 = 0x22;
    P1 = 0x01;

    // GPIO2
    SET_REG(PORTIDX, 2);
    PINMOD10 = 0x22;
    PINMOD32 = 0x22;
    PINMOD54 = 0x22;
    PINMOD76 = 0x22;
    P2 = 0x00;

    // GPIO3
    SET_REG(PORTIDX, 3);
    PINMOD10 = 0x22;
    PINMOD32 = 0x22;
    PINMOD54 = 0x22;
    PINMOD76 = 0x22;
    P3 = 0x00;

    // GPIO4
    SET_REG(PORTIDX, 4);
    PINMOD10 = 0x22;
    PINMOD32 = 0x22;
    PINMOD54 = 0x22;
    PINMOD76 = 0x22;
    P4 = 0x00;

    // GPIO5
    SET_REG(PORTIDX, 5);
    PINMOD10 = 0x22;
    PINMOD32 = 0x22;
    PINMOD54 = 0x22;
    PINMOD76 = 0x22;
    P5 = 0x00;

    REN = 0;
    ES = 0;
}

void halt_mode()
{
to_sleep:

    AUX2 = (AUX2 & ~AUX2_PWRSAV_MASK) | 1 << AUX2_PWRSAV_POS; // PWRSAV = 1，进一步降低功耗

    PCON = 0X02; // 进入halt模式。

    _nop_();
    _nop_();
    _nop_();

    AUX2 = (AUX2 & ~AUX2_PWRSAV_MASK) | 0 << AUX2_PWRSAV_POS; //   pwrsav = 0；

    if (!VCC_DETECT)
    {
        goto to_sleep;
    }
}

void enter_halt_mode()
{
    if (!VCC_DETECT)
    {
        if (halt_mode_time >= 200)
        {
            halt_mode_time = 0;
            CLRWDT = 1;

            gpio_seelp_init();

            halt_mode();

            bsp_init();
        }
    }
    else
    {
        halt_mode_time = 0;
    }
}
