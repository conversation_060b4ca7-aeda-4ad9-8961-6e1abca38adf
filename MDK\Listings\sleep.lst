C51 COMPILER V9.60.7.0   SLEEP                                                             07/26/2025 11:36:03 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE SLEEP
OBJECT MODULE PLACED IN .\Objects\sleep.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\sleep.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hardwa
                    -re) DEBUG OBJECTEXTEND PRINT(.\Listings\sleep.lst) TABS(2) OBJECT(.\Objects\sleep.obj)

line level    source

   1          #include "main.h"
   2          
   3          unsigned char test_buf[3] = {0};
   4          
   5          void gpio_seelp_init(void)
   6          {
   7   1          // GPIO0
   8   1          SET_REG(PORTIDX, 0);
   9   1          PINMOD10 = 0x22;
  10   1          PINMOD32 = 0x33;
  11   1          PINMOD54 = 0x22;
  12   1          PINMOD76 = 0x22;
  13   1          P0 = 0x0C;
  14   1      
  15   1          // GPIO1
  16   1          SET_REG(PORTIDX, 1);
  17   1          PINMOD10 = 0x29;
  18   1          PINMOD32 = 0x22;
  19   1          PINMOD54 = 0x22;
  20   1          PINMOD76 = 0x22;
  21   1          P1 = 0x01;
  22   1      
  23   1          // GPIO2
  24   1          SET_REG(PORTIDX, 2);
  25   1          PINMOD10 = 0x22;
  26   1          PINMOD32 = 0x22;
  27   1          PINMOD54 = 0x22;
  28   1          PINMOD76 = 0x22;
  29   1          P2 = 0x00;
  30   1      
  31   1          // GPIO3
  32   1          SET_REG(PORTIDX, 3);
  33   1          PINMOD10 = 0x22;
  34   1          PINMOD32 = 0x22;
  35   1          PINMOD54 = 0x22;
  36   1          PINMOD76 = 0x22;
  37   1          P3 = 0x00;
  38   1      
  39   1          // GPIO4
  40   1          SET_REG(PORTIDX, 4);
  41   1          PINMOD10 = 0x22;
  42   1          PINMOD32 = 0x22;
  43   1          PINMOD54 = 0x22;
  44   1          PINMOD76 = 0x22;
  45   1          P4 = 0x00;
  46   1      
  47   1          // GPIO5
  48   1          SET_REG(PORTIDX, 5);
  49   1          PINMOD10 = 0x22;
  50   1          PINMOD32 = 0x22;
  51   1          PINMOD54 = 0x22;
  52   1          PINMOD76 = 0x22;
  53   1          P5 = 0x00;
  54   1      
C51 COMPILER V9.60.7.0   SLEEP                                                             07/26/2025 11:36:03 PAGE 2   

  55   1          REN = 0;
  56   1          ES = 0;
  57   1      }
  58          
  59          void halt_mode()
  60          {
  61   1      to_sleep:
  62   1      
  63   1          AUX2 = (AUX2 & ~AUX2_PWRSAV_MASK) | 1 << AUX2_PWRSAV_POS; // PWRSAV = 1，进一步降低功耗
  64   1      
  65   1          PCON = 0X02; // 进入halt模式。
  66   1      
  67   1          _nop_();
  68   1          _nop_();
  69   1          _nop_();
  70   1      
  71   1          AUX2 = (AUX2 & ~AUX2_PWRSAV_MASK) | 0 << AUX2_PWRSAV_POS; //   pwrsav = 0；
  72   1      
  73   1          if (!VCC_DETECT)
  74   1          {
  75   2              goto to_sleep;
  76   2          }
  77   1      }
  78          
  79          void enter_halt_mode()
  80          {
  81   1          if (!VCC_DETECT)
  82   1          {
  83   2              if (halt_mode_time >= 200)
  84   2              {
  85   3                  halt_mode_time = 0;
  86   3                  CLRWDT = 1;
  87   3      
  88   3                  gpio_seelp_init();
  89   3      
  90   3                  halt_mode();
  91   3      
  92   3                  bsp_init();
  93   3              }
  94   2          }
  95   1          else
  96   1          {
  97   2              halt_mode_time = 0;
  98   2          }
  99   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    171    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =      3    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
