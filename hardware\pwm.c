#include "main.h"

#define BREATH_CNT_MAX          100
#define BREATH_FADE_MAX         1

// 显示LED呼吸效果专用参数 - 使用和按键LED相同的参数
#define DISPLAY_BREATH_CNT_MAX  100 // 和按键LED相同的PWM周期
#define DISPLAY_BREATH_FADE_MAX 1   // 和按键LED相同的渐变速度
#define DISPLAY_BREATH_DUTY_MAX 100 // PWM占空比范围0-100

uint8_t breath_bt_cnt = 0;
uint8_t breath_bt_dir = 0;
uint8_t breath_bt_fade = 0;
uint8_t breath_bt_duty = 0;

#if TIMER_BREATH_ENABLE
uint8_t breath_timer_cnt = 0;
uint8_t breath_timer_dir = 0;
uint8_t breath_timer_fade = 0;
uint8_t breath_timer_duty = 0;
#endif

#if AUX_BREATH_ENABLE
uint8_t breath_aux_cnt = 0;
uint8_t breath_aux_dir = 0;
uint8_t breath_aux_fade = 0;
uint8_t breath_aux_duty = 0;
#endif

#if SNOOZE_BREATH_ENABLE
uint8_t breath_snooze_cnt = 0;
uint8_t breath_snooze_dir = 0;
uint8_t breath_snooze_fade = 0;
uint8_t breath_snooze_duty = 0;
#endif

#if MUSIC_BREATH_ENABLE
uint8_t breath_music_cnt = 0;
uint8_t breath_music_dir = 0;
uint8_t breath_music_fade = 0;
uint8_t breath_music_duty = 0;
#endif

uint8_t breath_white_cnt = 0;
uint8_t breath_white_dir = 0;
uint8_t breath_white_fade = 0;
uint8_t breath_white_duty = 0;

uint8_t white_duty_config = 0;

// 根据当前环境灯亮度设置white_duty_config
void set_white_duty_config_by_ambient_light(void)
{
    switch (ambient_light)
    {
    case KEY_LED_LEVEL_0:
        white_duty_config = 0;
        break;
    case KEY_LED_LEVEL_1:
        white_duty_config = 25;
        break;
    case KEY_LED_LEVEL_2:
        white_duty_config = 50;
        break;
    case KEY_LED_LEVEL_3:
        white_duty_config = 75;
        break;
    case KEY_LED_LEVEL_4:
        white_duty_config = 100;
        break;
    default:
        white_duty_config = 100; // 默认最大亮度
        break;
    }
}

uint8_t pwm_led_on_flag = 0;  // 渐亮标志位
uint8_t pwm_led_off_flag = 0; // 渐灭标志位

uint8_t breath_power_cnt = 0;
uint8_t breath_power_fade = 0;
uint8_t breath_power_duty = 0;

uint8_t breath_off_cnt = 0;
uint8_t breath_off_fade = 0;
uint8_t breath_off_duty = 100;

// 显示时间LED呼吸效果控制变量
uint8_t display_led_breath_on_flag = 0;  // 显示LED渐亮标志位
uint8_t display_led_breath_off_flag = 0; // 显示LED渐灭标志位

uint8_t breath_display_on_cnt = 0;
uint8_t breath_display_on_fade = 0;
uint8_t breath_display_on_duty = 0;

uint8_t breath_display_off_cnt = 0;
uint8_t breath_display_off_fade = 0;
uint8_t breath_display_off_duty = 100;

void key_all_led(uint8_t state) // 1 关闭 0 开启
{
    KEY_LED_POWER = state;
    KEY_LED_VOLUME_DOWN = state;
    KEY_LED_VOLUME_UP = state;
    KEY_LED_AUX = state;
    KEY_LED_BT = state;
    KEY_LED_TIMER = state;
    KEY_LED_AMB = state;
    KEY_LED_MUSIC = state;
    KEY_LED_PP = state;
    // LED_SNOOZE = state;
}

void breath_led_exe(void) // 100us
{
    // 显示时间LED呼吸效果处理 - 渐亮
    if (display_led_breath_on_flag)
    {
        breath_display_on_cnt++;
        // LED_WHITE氛围灯呼吸效果控制 - 使用和按键LED相同的PWM方式
        if (breath_display_on_cnt < breath_display_on_duty)
        {
            LED_WHITE = 1; // 关闭氛围灯
        }
        else
        {
            LED_WHITE = 0; // 开启氛围灯
        }

        if (breath_display_on_cnt >= DISPLAY_BREATH_CNT_MAX)
        {
            breath_display_on_cnt = 0;
            breath_display_on_fade++;
            if (breath_display_on_fade >= DISPLAY_BREATH_FADE_MAX)
            {
                breath_display_on_fade = 0;
                breath_display_on_duty++;
                if (breath_display_on_duty >= DISPLAY_BREATH_DUTY_MAX)
                {
                    breath_display_on_duty = DISPLAY_BREATH_DUTY_MAX;
                    display_led_breath_on_flag = 0;
                    // 渐亮完成，LED_WHITE保持开启状态
                    LED_WHITE = 0;
                }
            }
        }
    }
    else
    {
        breath_display_on_cnt = 0;
    }

    // 显示时间LED呼吸效果处理 - 渐灭
    if (display_led_breath_off_flag)
    {
        breath_display_off_cnt++;
        // LED_WHITE氛围灯呼吸效果控制
        if (breath_display_off_cnt < breath_display_off_duty)
        {
            LED_WHITE = 1; // 关闭氛围灯
        }
        else
        {
            LED_WHITE = 0; // 开启氛围灯
        }

        if (breath_display_off_cnt >= DISPLAY_BREATH_CNT_MAX)
        {
            breath_display_off_cnt = 0;
            breath_display_off_fade++;
            if (breath_display_off_fade >= DISPLAY_BREATH_FADE_MAX)
            {
                breath_display_off_fade = 0;
                breath_display_off_duty--;
                if (breath_display_off_duty == 0)
                {
                    breath_display_off_duty = 0;
                    display_led_breath_off_flag = 0;
                    // 渐灭完成，LED_WHITE保持关闭状态
                    LED_WHITE = 1;
                }
            }
        }
    }
    else
    {
        breath_display_off_cnt = 0;
    }

#if 0
    if (time_state == STATE_POWER_OFF || time_state == STATE_SLEEP)
        return;
#else
    if (pwm_led_on_flag)
    {
        breath_power_cnt++;
        if (breath_power_cnt < breath_power_duty)
        {
            key_all_led(0);
        }
        else
        {
            key_all_led(1);
        }

        if (breath_power_cnt >= BREATH_CNT_MAX)
        {
            breath_power_cnt = 0;
            breath_power_fade++;
            if (breath_power_fade >= BREATH_FADE_MAX)
            {
                breath_power_fade = 0;
                breath_power_duty++;
                if (breath_power_duty >= 100)
                {
                    breath_power_duty = 0;
                    key_all_led(0);
                    pwm_led_on_flag = 0;
                }
            }
        }

        return;
    }
    else
    {
        breath_power_cnt = 0;
    }

    if (pwm_led_off_flag)
    {
        breath_off_cnt++;
        if (breath_off_cnt < breath_off_duty)
        {
            key_all_led(0);
        }
        else
        {
            key_all_led(1);
        }

        if (breath_off_cnt >= BREATH_CNT_MAX)
        {
            breath_off_cnt = 0;
            breath_off_fade++;
            if (breath_off_fade >= BREATH_FADE_MAX)
            {
                breath_off_fade = 0;
                breath_off_duty--;
                if (breath_off_duty == 0)
                {
                    breath_off_duty = 100;
                    key_all_led(1);
                    LED_SNOOZE = 1;
                    pwm_led_off_flag = 0;
                }
            }
        }
        return;
    }
    else
    {
        breath_off_cnt = 0;
    }

#endif

    if (time_state == STATE_POWER_OFF || time_state == STATE_SLEEP)
        return;

#if BT_MODE_BREATH_ENABLE
    if (music_mode == MODE_BT || music_mode == MODE_BT_RECONNECT)
#endif
        if (music_mode == MODE_BT_PAIR)
        {
            breath_bt_cnt++;
            if (breath_bt_cnt < breath_bt_duty)
            {
                KEY_LED_BT = 0;
            }
            else
            {
                KEY_LED_BT = 1;
            }
        }
        else
        {
            breath_bt_cnt = 0;
        }

#if TIMER_BREATH_ENABLE
    if (count_down_flag && !already_countdown)
    {
        breath_timer_cnt++;
        if (breath_timer_cnt < breath_timer_duty)
        {
            KEY_LED_TIMER = 0;
        }
        else
        {
            KEY_LED_TIMER = 1;
        }
    }
    else
    {
        breath_timer_cnt = 0;
    }

    if (breath_timer_cnt >= BREATH_CNT_MAX)
    {
        breath_timer_cnt = 0;
        breath_timer_fade++;
        if (breath_timer_fade >= BREATH_FADE_MAX)
        {
            breath_timer_fade = 0;
            if (breath_timer_dir)
            {
                breath_timer_duty--;
                if (breath_timer_duty == 0)
                {
                    breath_timer_dir = 0;
                }
            }
            else
            {
                breath_timer_duty++;
                if (breath_timer_duty >= 100)
                {
                    breath_timer_duty = 100;
                    breath_timer_dir = 1;
                }
            }
        }
    }

#endif

#if AUX_BREATH_ENABLE

    if (music_mode == MODE_AUX)
    {
        breath_aux_cnt++;
        if (breath_aux_cnt < breath_aux_duty)
        {
            KEY_LED_AUX = 0;
        }
        else
        {
            KEY_LED_AUX = 1;
        }
    }
    else
    {
        breath_aux_cnt = 0;
        KEY_LED_AUX = 0;
    }

    if (breath_aux_cnt >= BREATH_CNT_MAX)
    {
        breath_aux_cnt = 0;
        breath_aux_fade++;
        if (breath_aux_fade >= BREATH_FADE_MAX)
        {
            breath_aux_fade = 0;
            if (breath_aux_dir)
            {
                breath_aux_duty--;
                if (breath_aux_duty == 0)
                {
                    breath_aux_dir = 0;
                }
            }
            else
            {
                breath_aux_duty++;
                if (breath_aux_duty >= 100)
                {
                    breath_aux_duty = 100;
                    breath_aux_dir = 1;
                }
            }
        }
    }

#endif

#if SNOOZE_BREATH_ENABLE

    if (alarm_state)
    {
        breath_snooze_cnt++;
        if (breath_snooze_cnt < breath_snooze_duty)
        {
            LED_SNOOZE = 0;
        }
        else
        {
            LED_SNOOZE = 1;
        }
    }
    else
    {
        breath_snooze_cnt = 0;
    }

    if (breath_snooze_cnt >= BREATH_CNT_MAX)
    {
        breath_snooze_cnt = 0;
        breath_snooze_fade++;
        if (breath_snooze_fade >= BREATH_FADE_MAX)
        {
            breath_snooze_fade = 0;
            if (breath_snooze_dir)
            {
                breath_snooze_duty--;
                if (breath_snooze_duty == 0)
                {
                    breath_snooze_dir = 0;
                }
            }
            else
            {
                breath_snooze_duty++;
                if (breath_snooze_duty >= 100)
                {
                    breath_snooze_duty = 100;
                    breath_snooze_dir = 1;
                }
            }
        }
    }

#endif

#if MUSIC_BREATH_ENABLE

    if (music_mode == MODE_AMBIENT)
    {
        breath_music_cnt++;
        if (breath_music_cnt < breath_music_duty)
        {
            KEY_LED_MUSIC = 0;
        }
        else
        {
            KEY_LED_MUSIC = 1;
        }
    }
    else
    {
        breath_music_cnt = 0;
        KEY_LED_MUSIC = 0;
    }

    if (breath_music_cnt >= BREATH_CNT_MAX)
    {
        breath_music_cnt = 0;
        breath_music_fade++;
        if (breath_music_fade >= BREATH_FADE_MAX)
        {
            breath_music_fade = 0;
            if (breath_music_dir)
            {
                breath_music_duty--;
                if (breath_music_duty == 0)
                {
                    breath_music_dir = 0;
                }
            }
            else
            {
                breath_music_duty++;
                if (breath_music_duty >= 100)
                {
                    breath_music_duty = 100;
                    breath_music_dir = 1;
                }
            }
        }
    }

#endif

    if (alarm_state || music_mode == MODE_BT_PAIR)
    {
        breath_white_cnt++;
        if (breath_white_cnt < breath_white_duty)
        {
            LED_WHITE = 0;
        }
        else
        {
            LED_WHITE = 1;
        }
    }
    else
    {
        breath_white_cnt = 0;
        // 重置呼吸效果状态，确保下次进入呼吸模式时状态正确
        breath_white_duty = 0;
        breath_white_dir = 0;
        breath_white_fade = 0;
    }

    if (breath_bt_cnt >= BREATH_CNT_MAX)
    {
        breath_bt_cnt = 0;
        breath_bt_fade++;
        if (breath_bt_fade >= BREATH_FADE_MAX)
        {
            breath_bt_fade = 0;
            if (breath_bt_dir)
            {
                breath_bt_duty--;
                if (breath_bt_duty == 0)
                {
                    breath_bt_dir = 0;
                }
            }
            else
            {
                breath_bt_duty++;
                if (breath_bt_duty >= 100)
                {
                    breath_bt_duty = 100;
                    breath_bt_dir = 1;
                }
            }
        }
    }

    if (breath_white_cnt >= BREATH_CNT_MAX)
    {
        breath_white_cnt = 0;
        breath_white_fade++;
        if (breath_white_fade >= BREATH_FADE_MAX)
        {
            breath_white_fade = 0;
            if (breath_white_dir)
            {
                breath_white_duty--;
                if (breath_white_duty == 0)
                {
                    breath_white_dir = 0;
                }
            }
            else
            {
                breath_white_duty++;
                if (breath_white_duty >= white_duty_config)
                {
                    breath_white_duty = white_duty_config;
                    breath_white_dir = 1;
                }
            }
        }
    }
}
