#ifndef __PWM_H__
#define __PWM_H__

extern uint8_t white_duty_config;
extern uint8_t pwm_led_on_flag;
extern uint8_t pwm_led_off_flag;

// 显示时间LED呼吸效果控制标志
extern uint8_t display_led_breath_on_flag;  // 显示LED渐亮标志位
extern uint8_t display_led_breath_off_flag; // 显示LED渐灭标志位

// 显示时间LED呼吸效果计数器
extern uint8_t breath_display_on_cnt;
extern uint8_t breath_display_off_cnt;
extern uint8_t breath_display_on_duty;
extern uint8_t breath_display_off_duty;

// 显示LED呼吸效果专用参数
#define DISPLAY_BREATH_CNT_MAX 100

#define BT_MODE_BREATH_ENABLE  0
#define AUX_BREATH_ENABLE      0
#define MUSIC_BREATH_ENABLE    0
#define TIMER_BREATH_ENABLE    0
#define SNOOZE_BREATH_ENABLE   1

void breath_led_exe(void);
void set_white_duty_config_by_ambient_light(void);

#endif
