C51 COMPILER V9.60.7.0   ENCODER                                                           04/07/2025 15:55:46 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE ENCODER
OBJECT MODULE PLACED IN .\Objects\encoder.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\encoder.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hard
                    -ware) DEBUG OBJECTEXTEND PRINT(.\Listings\encoder.lst) TABS(2) OBJECT(.\Objects\encoder.obj)

line level    source

   1          #include "main.h"
   2          
   3          encoder_t encoder;
   4          
   5          uint8_t encoder_scan_time = 0;
   6          static uint8_t inc_count = 0; // 正向旋转计数
   7          static uint8_t dec_count = 0; // 负向旋转计数
   8          
   9          void pwm_config_init(unsigned int prd, unsigned int duty, unsigned char ch)
  10          {
  11   1          PWMIDX = ch;
  12   1          PWMPRDL = prd & 0xff; // 写 先低后高   读 先高后低
  13   1          PWMPRDH = (prd >> 8) & 0XFF;
  14   1      
  15   1          PWMIDX = ch;
  16   1          PWMDL = duty & 0XFF;        // PWM1DL 占空比低字节
  17   1          PWMDH = (duty >> 8) & 0XFF; // PWM1DL 占空比低字节
  18   1      }
  19          
  20          void encoder_init()
  21          {
  22   1          SET_REG(PORTIDX, 2);
  23   1          SET_REG_BITS(PINMOD32, PINMOD3, PIN_MODE_OD_IPU);
  24   1          P2_3 = 1;
  25   1      
  26   1          SET_REG(PORTIDX, 4);
  27   1          SET_REG_BITS(PINMOD10, PINMOD0, PIN_MODE_OD_IPU);
  28   1          P4_0 = 1;
  29   1      
  30   1          // pwm
  31   1          SET_REG(PORTIDX, 0);
  32   1          SET_REG_BITS(PINMOD10, PINMOD0, PIN_MODE_AF); // 00
  33   1          SET_REG_BITS(PINMOD10, PINMOD1, PIN_MODE_AF); // 01
  34   1          SET_REG_BITS(PWMEN, PWM3EN, 1);
  35   1          SET_REG_BITS(PWMCON, PWM3CKS, 2);
  36   1      
  37   1          SET_REG(PORTIDX, 5);
  38   1          SET_REG_BITS(PINMOD76, PINMOD7, PIN_MODE_AF); // 57
  39   1          SET_REG_BITS(PWMEN, PWM2EN, 1);
  40   1          PWM2CON = 0x80;
  41   1          SET_REG_BITS(PWMCON, PWM2CKS, 2);
  42   1      
  43   1      
  44   1          encoder.state = 0;
  45   1          encoder.debounce = 0;
  46   1          encoder.curr = 0;
  47   1          encoder.bkp = 0;
  48   1      }
  49          
  50          void encoder_inc_exe(void)
  51          {
  52   1          dec_count = 0; // 重置负向计数
  53   1          inc_count++;
  54   1          if (inc_count >= 1)
C51 COMPILER V9.60.7.0   ENCODER                                                           04/07/2025 15:55:46 PAGE 2   

  55   1          {
  56   2              inc_count = 0;
  57   2              if (key_led_light < KEY_LED_LEVEL_4)
  58   2              { // 最大值为 LEVEL_4
  59   3                  key_led_light++;
  60   3              }
  61   2          }
  62   1      }
  63          
  64          void encoder_dec_exe(void)
  65          {
  66   1          inc_count = 0; // 重置正向计数
  67   1          dec_count++;
  68   1          if (dec_count >= 1)
  69   1          {
  70   2              dec_count = 0;
  71   2              if (key_led_light > KEY_LED_LEVEL_1)
  72   2              { // 最小值为 LEVEL_1
  73   3                  key_led_light--;
  74   3              }
  75   2          }
  76   1      }
  77          
  78          void encoder_exec(void)
  79          {
  80   1          uint8_t inc, dec;
  81   1      
  82   1          inc = IO_HIGH;
  83   1          dec = IO_LOW;
  84   1      
  85   1          if (inc == dec)
  86   1          {
  87   2              encoder.state = inc;
  88   2          }
  89   1          else
  90   1          {
  91   2              if (inc != encoder.bkp)
  92   2                  encoder.state = ENC_INC;
  93   2              else
  94   2                  encoder.state = ENC_DEC;
  95   2          }
  96   1      
  97   1          if (encoder.curr != encoder.state)
  98   1          {
  99   2      
 100   2              if (++encoder.debounce >= 2)
 101   2              {
 102   3                  encoder.debounce = 0;
 103   3                  if (encoder.state == ENC_INC)
 104   3                  {
 105   4                      encoder_inc_exe();
 106   4                  }
 107   3                  else if (encoder.state == ENC_DEC)
 108   3                  {
 109   4                      encoder_dec_exe();
 110   4                  }
 111   3                  if (encoder.state < 2)
 112   3                  {
 113   4                      encoder.bkp = inc;
 114   4                  }
 115   3                  encoder.curr = encoder.state;
 116   3              }
C51 COMPILER V9.60.7.0   ENCODER                                                           04/07/2025 15:55:46 PAGE 3   

 117   2          }
 118   1          else
 119   1          {
 120   2              encoder.debounce = 0;
 121   2          }
 122   1      }
 123          
 124          void encoder_task()
 125          {
 126   1          if (time_state == STATE_POWER_OFF)
 127   1              return;
 128   1      
 129   1          if (encoder_scan_time > 5)
 130   1          {
 131   2              encoder_scan_time = 0;
 132   2              encoder_exec();
 133   2          }
 134   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    318    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =      7       2
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
