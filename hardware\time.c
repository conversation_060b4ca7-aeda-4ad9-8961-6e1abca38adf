#include "main.h"

void hour_inc(unsigned char *hour)
{
    (*hour)++;
    if (*hour >= 24)
    {
        *hour = 0;
    }
}

void hour_dec(unsigned char *hour)
{
    if (*hour == 0)
    {
        *hour = 23;
    }
    else
    {
        (*hour)--;
    }
}

void minute_inc(unsigned char *minute)
{
    (*minute)++;
    if (*minute >= 60)
    {
        *minute = 0;
    }
}

void minute_dec(unsigned char *minute)
{
    if (*minute == 0)
    {
        *minute = 59;
    }
    else
    {
        (*minute)--;
    }
}

void change_countdown_time_inc()
{
    if (rtc.countdown_time == 0)
    {
        rtc.countdown_time = 15;
    }
    else if (rtc.countdown_time == 15)
    {
        rtc.countdown_time = 30;
    }
    else if (rtc.countdown_time == 30)
    {
        rtc.countdown_time = 60;
    }
    else if (rtc.countdown_time == 60)
    {
        rtc.countdown_time = 0;
    }
    else
    {
        // 如果值不在预期范围内，修正为最接近的有效值
        if (rtc.countdown_time < 8) // 0-7 -> 0
        {
            rtc.countdown_time = 0;
        }
        else if (rtc.countdown_time < 23) // 8-22 -> 15
        {
            rtc.countdown_time = 15;
        }
        else if (rtc.countdown_time < 45) // 23-44 -> 30
        {
            rtc.countdown_time = 30;
        }
        else // 45+ -> 60
        {
            rtc.countdown_time = 60;
        }
    }
}

void change_countdown_time_dec()
{
    if (rtc.countdown_time == 15)
    {
        rtc.countdown_time = 0;
    }
    else if (rtc.countdown_time == 30)
    {
        rtc.countdown_time = 15;
    }
    else if (rtc.countdown_time == 60)
    {
        rtc.countdown_time = 30;
    }
    else if (rtc.countdown_time == 0)
    {
        rtc.countdown_time = 60;
    }
    else
    {
        // 如果值不在预期范围内，修正为最接近的有效值
        if (rtc.countdown_time < 8) // 0-7 -> 0
        {
            rtc.countdown_time = 0;
        }
        else if (rtc.countdown_time < 23) // 8-22 -> 15
        {
            rtc.countdown_time = 15;
        }
        else if (rtc.countdown_time < 45) // 23-44 -> 30
        {
            rtc.countdown_time = 30;
        }
        else // 45+ -> 60
        {
            rtc.countdown_time = 60;
        }
    }
}

void change_snooze_time()
{
    if (rtc.snooze_time == 5)
    {
        rtc.snooze_time = 15;
    }
    else if (rtc.snooze_time == 15)
    {
        rtc.snooze_time = 20;
    }
    else if (rtc.snooze_time == 20)
    {
        rtc.snooze_time = 5;
    }
    else
    {
        // 如果值不在预期范围内，修正为最接近的有效值
        if (rtc.snooze_time < 10) // 0-9 -> 5
        {
            rtc.snooze_time = 5;
        }
        else if (rtc.snooze_time < 18) // 10-17 -> 15
        {
            rtc.snooze_time = 15;
        }
        else // 18+ -> 20
        {
            rtc.snooze_time = 20;
        }
    }

    // 记录当前设置的贪睡时间，供下次使用
    last_snooze_time = rtc.snooze_time;
}
