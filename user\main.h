#ifndef __MAIN_H__
#define __MAIN_H__

#define TEST_MODE_ENABLE 0

typedef unsigned char uint8_t;
typedef unsigned int uint16_t;
typedef unsigned long uint32_t;

#include "TM52F1386_bsp.h"
#include <REGtenxTM52eF1385.h>
#include <intrins.h>

#include "alarm.h"
#include "flash.h"
#include "iic.h"
#include "key.h"
#include "key_driver.h"
#include "led.h"
#include "ledstate.h"
#include "pwm.h"
#include "sleep.h"
#include "switch.h"
#include "time.h"
#include "uart.h"

#define FRC_HZ              18432000
#define FRC_DIV             1
#define CLK_US              100                                                                       // 100us
#define TIME_RELOAD         (unsigned int)(65536 - (((FRC_HZ / FRC_DIV) / 2 / 1000) * CLK_US / 1000)) // 精度更高

#define DEFAULT_IDL_VOL     8
#define DEFAULT_BT_VOL      8
#define DEFAULT_AUX_VOL     8
#define DEFAULT_AMBIENT_VOL 8

#define KEY_LED_LEVEL_0     0
#define KEY_LED_LEVEL_1     1
#define KEY_LED_LEVEL_2     2
#define KEY_LED_LEVEL_3     3
#define KEY_LED_LEVEL_4     4

typedef struct s_rtc
{
    uint8_t second;         // 秒
    uint8_t minute;         // 分
    uint8_t hour;           // 时
    uint8_t alarm_hour;     // 闹钟时
    uint8_t alarm_minute;   // 闹钟分
    uint8_t countdown_time; // 倒计时时间
    uint8_t snooze_time;    // 贪睡时间
} rtc_t;

typedef struct
{
    uint8_t idl_vol;
    uint8_t bt_vol;
    uint8_t aux_vol;
    uint8_t ambient_vol;
} vol_t;

enum e_state
{
    STATE_POWER_OFF,
    STATE_SLEEP, // 休眠
    STATE_REAL_TIME,
    STATE_SET_TIME_HOUR,
    STATE_SET_TIME_MINUTE,
    STATE_SET_ALARM_HOUR,
    STATE_SET_ALARM_MINUTE,
    STATE_COUNT_DOWN,
    STATE_SET_COUNT_DOWN,
    STATE_SET_BT_NUM_THOUSAND,
    STATE_SET_BT_NUM_HUNDRED,
    STATE_SET_BT_NUM_TEN,
    STATE_SET_BT_NUM_ONE,
    STATE_BT_NUM,
    STATE_SNOOZE_CNT,
    STATE_SET_SNOOZE_CNT
};

enum e_music_mode
{
    MODE_IDE,          // 空闲模式
    MODE_BT,           // 蓝牙模式
    MODE_BT_PAIR,      // 蓝牙配对模式
    MODE_BT_RECONNECT, // 蓝牙回连模式
    MODE_AUX,          // AUX模式
    MODE_AMBIENT       // 睡眠白噪音模式
};

extern uint8_t scan_key_time;
extern uint8_t countdown_time_ended;
extern uint8_t first_power_on;

extern rtc_t rtc;
extern vol_t vol;
extern enum e_state time_state;
extern enum e_music_mode music_mode;
extern uint16_t led_light_num;

extern uint8_t halt_mode_flag;  // 进入halt模式标志
extern uint16_t halt_mode_time; // halt模式时间

extern uint8_t key_led_light;
extern uint8_t ambient_light;
extern uint8_t ambient_light_before_sleep;   // 进入睡眠前的环境灯状态
extern enum e_state time_state_before_sleep; // 进入睡眠前的时间状态
extern uint8_t alarm_wakeup_flag;            // 闹钟响起后触摸唤醒标志

extern uint8_t snooze_time_ended; // 贪睡时间结束标志

extern uint8_t mute_key_sound_in_music; // 播放音频时是否屏蔽按键音，1=屏蔽，0=不屏蔽
extern uint8_t aux_flash_flag;          // AUX灯闪烁标志

void timer0_init();
void tim3_init(void);
void bsp_init();

#endif
