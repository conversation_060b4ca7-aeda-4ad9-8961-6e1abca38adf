C51 COMPILER V9.60.7.0   ISR                                                               07/28/2025 10:24:16 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE ISR
OBJECT MODULE PLACED IN .\Objects\isr.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\user\isr.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hardware) DE
                    -BUG OBJECTEXTEND PRINT(.\Listings\isr.lst) TABS(2) OBJECT(.\Objects\isr.obj)

line level    source

   1          #include "main.h"
   2          
   3          uint8_t t1ms_cnt = 0;
   4          uint8_t t10ms_cnt = 0;
   5          uint8_t scan_key_time = 0;
   6          uint8_t countdown_time_ended = 0;
   7          uint8_t first_power_on = 0;
   8          uint8_t led_light = 0;
   9          uint16_t led_light_num = 0;
  10          uint8_t key_led_light = 0;
  11          uint8_t ambient_light = 0;
  12          uint8_t led_light_sctime = 0;
  13          
  14          uint8_t snooze_time_ended = 0; // 贪睡时间结束标志
  15          
  16          uint8_t halt_mode_flag = 0;  // 进入halt模式标志 1:进入halt模式 0:退出halt模式
  17          uint16_t halt_mode_time = 0; // halt模式时间
  18          
  19          void timer0_init()
  20          {
  21   1          TMOD = (TMOD & ~TMOD_TMOD0_MASK) | 1 << TMOD_TMOD0_POS; // Timer0模式选择  16位定时器
  22   1      
  23   1          TH0 = (TIME_RELOAD) / 256; // 100US
  24   1          TL0 = (TIME_RELOAD) % 256;
  25   1      
  26   1          TR0 = 1; // TIMER0 运行
  27   1          ET0 = 1;
  28   1      }
  29          
  30          void tim3_init(void)
  31          {
  32   1          SET_REG_BITS(OPTION, TM3CKS, 0); // Timer3慢时钟 (SXT)   32.768K
  33   1          SET_REG_BITS(OPTION, TM3PSC, 1); // 中断率控制选择16384慢时钟周期
  34   1          SET_REG_BITS(INTE1, TM3IE, 1);   // 允许Timer3 中断使能控制
  35   1      
  36   1          // LVRCON = 0x50;
  37   1          LVRCON |= 0x40;
  38   1          SCKTYPE = 1;                        // SXT
  39   1          PORTIDX = PORT0;                    // 初始化晶振引脚
  40   1          PINMOD32 = PINMOD32 & ~0xff | 0x33; // P03  P02
  41   1      }
  42          
  43          void timer0_int() interrupt 1
  44          {
  45   1          TH0 = (TIME_RELOAD) / 256; // 100US
  46   1          TL0 = (TIME_RELOAD) % 256;
  47   1      
  48   1          t1ms_cnt++;
  49   1          scan_key_time++;
  50   1          led_light_sctime++;
  51   1          breath_led_exe();
  52   1      
  53   1          if (led_light_sctime > 3)
  54   1          {
C51 COMPILER V9.60.7.0   ISR                                                               07/28/2025 10:24:16 PAGE 2   

  55   2              led_light_sctime = 0;
  56   2          }
  57   1      
  58   1          if (time_state == STATE_POWER_OFF)
  59   1          {
  60   2              LED_WHITE = 0;
  61   2          }
  62   1          else if (time_state == STATE_SLEEP)
  63   1          {
  64   2              // 睡眠状态下，根据进入睡眠前的环境灯状态决定LED_WHITE的状态
  65   2              if (ambient_light_before_sleep == KEY_LED_LEVEL_0)
  66   2              {
  67   3                  LED_WHITE = 1; // 如果进入睡眠前是关的，保持关闭
  68   3              }
  69   2              else
  70   2              {
  71   3                  // 如果闹钟响起，让氛围灯呼吸而不是常亮
  72   3                  if (alarm_state)
  73   3                  {
  74   4                      // 闹钟状态下由breath_led_exe控制呼吸效果，这里不控制LED_WHITE
  75   4                  }
  76   3                  else
  77   3                  {
  78   4                      // 如果进入睡眠前是开的，保持原来的PWM控制
  79   4                      if (led_light_sctime < ambient_light_before_sleep)
  80   4                      {
  81   5                          LED_WHITE = 0;
  82   5                      }
  83   4                      else
  84   4                      {
  85   5                          LED_WHITE = 1;
  86   5                      }
  87   4                  }
  88   3              }
  89   2          }
  90   1          else if (ble_pair_flag) // 蓝牙配对模式
  91   1          {
  92   2              // MODE_BT_PAIR模式下，LED_WHITE由breath_led_exe()中的呼吸效果控制
  93   2              // 这里不控制LED_WHITE，避免与呼吸效果冲突
  94   2          }
  95   1          else if (alarm_state)
  96   1          {
  97   2              // 处于闹铃状态时，由breath_led_exe控制呼吸效果
  98   2              // 不在这里控制LED_WHITE
  99   2          }
 100   1          else
 101   1          {
 102   2              if (led_light_sctime < ambient_light)
 103   2              {
 104   3                  LED_WHITE = 0;
 105   3              }
 106   2              else
 107   2              {
 108   3                  LED_WHITE = 1;
 109   3              }
 110   2          }
 111   1      
 112   1          // 数码管显示控制 - 使用高频扫描避免闪烁
 113   1          if (display_led_breath_on_flag || display_led_breath_off_flag)
 114   1          {
 115   2              // 呼吸效果期间，调整数码管亮度而不是开关
 116   2              uint8_t display_brightness = key_led_light;
C51 COMPILER V9.60.7.0   ISR                                                               07/28/2025 10:24:16 PAGE 3   

 117   2      
 118   2              if (display_led_breath_on_flag)
 119   2              {
 120   3                  // 渐亮：根据呼吸进度调整亮度 (0-100 映射到 0-key_led_light)
 121   3                  display_brightness = (key_led_light * breath_display_on_duty) / 100;
 122   3                  if (display_brightness == 0 && breath_display_on_duty > 0)
 123   3                      display_brightness = 1; // 确保有最小亮度
 124   3              }
 125   2              else if (display_led_breath_off_flag)
 126   2              {
 127   3                  // 渐灭：根据呼吸进度调整亮度 (100-0 映射到 key_led_light-0)
 128   3                  display_brightness = (key_led_light * breath_display_off_duty) / 100;
 129   3              }
 130   2      
 131   2              // 使用调整后的亮度进行高频扫描，保持400us周期
 132   2              if (led_light_sctime < display_brightness)
 133   2              {
 134   3                  led_task(); // 正常显示数码管
 135   3              }
 136   2              else
 137   2              {
 138   3                  all_led_seg_off();
 139   3              }
 140   2          }
 141   1          else
 142   1          {
 143   2              // 正常模式下的数码管控制
 144   2              if (led_light_sctime < key_led_light)
 145   2              {
 146   3                  led_task();
 147   3              }
 148   2              else
 149   2              {
 150   3                  all_led_seg_off();
 151   3              }
 152   2          }
 153   1      
 154   1          if (t1ms_cnt >= 10) // 1ms
 155   1          {
 156   2              t1ms_cnt = 0;
 157   2              scan_switch_time++;
 158   2              scan_flash_time++;
 159   2              t10ms_cnt++;
 160   2              halt_mode_time++;
 161   2              if (t10ms_cnt >= 100) // 100ms
 162   2              {
 163   3                  t10ms_cnt = 0;
 164   3                  led_state_scan_time++;
 165   3              }
 166   2          }
 167   1      
 168   1          // 防溢出
 169   1          if (scan_key_time >= 200)
 170   1              scan_key_time = 0;
 171   1      
 172   1          if (scan_switch_time >= 200)
 173   1              scan_switch_time = 0;
 174   1      
 175   1          // 防止halt_mode_time溢出
 176   1          if (halt_mode_time >= 60000) // 60秒后重置，避免溢出
 177   1              halt_mode_time = 0;
 178   1      }
C51 COMPILER V9.60.7.0   ISR                                                               07/28/2025 10:24:16 PAGE 4   

 179          
 180          void tim3() interrupt 7
 181          {
 182   1          if (first_power_on)
 183   1              return;
 184   1          // 实时时间
 185   1          rtc.second++;
 186   1      #if TEST_MODE_ENABLE
                  if (rtc.second >= 4)
              #else
 189   1          if (rtc.second >= 120) // 1min
 190   1      #endif
 191   1          {
 192   2              rtc.second = 0;
 193   2              rtc.minute++;
 194   2              if (rtc.minute >= 60) // 1h
 195   2              {
 196   3                  rtc.minute = 0;
 197   3                  rtc.hour++;
 198   3                  if (rtc.hour >= 24)
 199   3                  {
 200   4                      rtc.hour = 0;
 201   4                  }
 202   3              }
 203   2      
 204   2              // 倒计时
 205   2              if (count_down_flag)
 206   2              {
 207   3                  if (rtc.countdown_time > 0)
 208   3                  {
 209   4                      rtc.countdown_time--;
 210   4                  }
 211   3      
 212   3                  if (rtc.countdown_time == 0)
 213   3                  {
 214   4                      countdown_time_ended = 1;
 215   4                  }
 216   3              }
 217   2      
 218   2              // 贪睡
 219   2              if (snooze_state)
 220   2              {
 221   3                  if (rtc.snooze_time > 0)
 222   3                  {
 223   4                      rtc.snooze_time--;
 224   4                  }
 225   3      
 226   3                  if (rtc.snooze_time == 0)
 227   3                  {
 228   4                      snooze_time_ended = 1;
 229   4                  }
 230   3              }
 231   2      
 232   2              // 闹钟自动关闭计时
 233   2              if (alarm_state && alarm_auto_close_time > 0)
 234   2              {
 235   3                  alarm_auto_close_time--;
 236   3                  if (alarm_auto_close_time == 0)
 237   3                  {
 238   4                      // 3分钟时间到，设置自动关闭标志
 239   4                      alarm_auto_close_flag = 1;
 240   4                  }
C51 COMPILER V9.60.7.0   ISR                                                               07/28/2025 10:24:16 PAGE 5   

 241   3              }
 242   2          }
 243   1      }
 244          
 245          void port_irq() interrupt 8
 246          {
 247   1          if (INTFLG & PCIF)
 248   1          {
 249   2              INTFLG = INTFLG & ~PCIF;
 250   2              if (KEY_INT)
 251   2              {
 252   3                  scan_key_enable = 1;
 253   3              }
 254   2              else
 255   2              {
 256   3                  scan_key_enable = 0;
 257   3              }
 258   2      
 259   2              if (VCC_DETECT && time_state == STATE_POWER_OFF)
 260   2              {
 261   3                  vcc_detect_flag = 1;
 262   3                  halt_mode_flag = 0;
 263   3              }
 264   2              else if (!VCC_DETECT)
 265   2              {
 266   3                  vcc_detect_flag = 0;
 267   3                  halt_mode_flag = 1;
 268   3              }
 269   2          }
 270   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    678    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =     15       1
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
