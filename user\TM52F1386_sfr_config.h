#ifndef __TM52F1386_SFR_CONFIG_H__
#define __TM52F1386_SFR_CONFIG_H__


#define P0_MASK				       (0xff)


/*--------INTPORT---------------*/
#define INTPORT_MASK			   (0xff)
#define INTPORT_P0IF_MASK  		   (0x01)    
#define INTPORT_P0IF_POS		   (0)
#define INTPORT_P1IF_MASK  		   (0x02)    
#define INTPORT_P1IF_POS		   (1)
#define INTPORT_P2IF_MASK  		   (0x04)    
#define INTPORT_P2IF_POS		   (2)
#define INTPORT_P3IF_MASK  		   (0x08)    
#define INTPORT_P3IF_POS	       (3)
#define INTPORT_P4IF_MASK  		   (0x10)    
#define INTPORT_P4IF_POS	       (4)
#define INTPORT_P5IF_MASK  		   (0x20)    
#define INTPORT_P5IF_POS	       (5)

/*--------INTPWM---------------*/
#define INTPWM_MASK                (0XFF)
#define INTPWM_PWM0IF_MASK         (0X01)
#define INTPWM_PWM0IF_POS          (0)
#define INTPWM_PWM1IF_MASK         (0X02)
#define INTPWM_PWM1IF_POS          (1)
#define INTPWM_PWM2IF_MASK         (0X04)
#define INTPWM_PWM2IF_POS          (2)
#define INTPWM_PWM3IF_MASK         (0X08)
#define INTPWM_PWM3IF_POS          (3)

/*-------PCON*******/
#define PCON_MASK                  (0XFF)

#define PCON_IDL_MASK              (0X01)
#define PCON_IDL_POS	           (0)
#define PCON_PD_MASK               (0X02)
#define PCON_PD_POS                (1)
#define PCON_GF0_MASK		       (0x04)
#define PCON_GF0_POS		       (2)
#define PCON_GF1_MASK		       (0x08)
#define PCON_GF1_POS		       (3)
#define PCON_SMOD_MASK             (0x80)
#define PCON_SMOD_POS		       (7)

/*--------TCON---------------*/
#define TCON_MASK			       (0xff)
#define TCON_IT0_MASK		       (0x01)
#define TCON_IT0_POS			   (0)
#define TCON_IE0_MASK			   (0x02)
#define TCON_IE0_POS			   (1)
#define TCON_IT1_MASK			   (0x04)
#define TCON_IT1_POS			   (2)
#define TCON_IE1_MASK			   (0x08)
#define TCON_IE1_POS			   (3)
#define TCON_TR0_MASK			   (0x10)
#define TCON_TR0_POS			   (4)
#define TCON_TF0_MASK			   (0x20)
#define TCON_TF0_POS			   (5)
#define TCON_TR1_MASK			   (0x40)
#define TCON_TR1_POS			   (6)
#define TCON_TF1_MASK			   (0x80)
#define TCON_TF1_POS		       (7)

/*--------TMOD---------------*/
#define TMOD_MASK				   (0xff)
#define TMOD_TMOD0_MASK		       (0x03)
#define TMOD_TMOD0_POS			   (0)
#define TMOD_CT0N_MASK		       (0x04)
#define TMOD_CT0N_POS			   (2)
#define TMOD_GATE0_MASK			   (0x08)
#define TMOD_GATE0_POS			   (3)
#define TMOD_TMOD1_MASK			   (0x30)
#define TMOD_TMOD1_POS			   (4)
#define TMOD_CT1N_MASK			   (0x40)
#define TMOD_CT1N_POS			   (6)
#define TMOD_GATE1_MASK			   (0x80)
#define TMOD_GATE1_POS			   (7)

//TL0
#define TL0_MASK			       (0xff)
#define TL0_TL0_MASK		       (0xff)
#define TL0_TL0_POS				   (0)

#define TL1_MASK				   (0xff)
#define TL1_TL1_MASK			   (0xff)
#define TL1_TL1_POS				   (0)

#define TH0_MASK				   (0xff)
#define TH0_TH0_MASK			   (0xff)
#define TH0_TH0_POS				   (0)

#define TH1_MASK				   (0xff)
#define TH1_TH1_MASK			   (0xff)
#define TH1_TH1_POS				   (0)

/*--------SCON2---------------*/
#define SCON2_MASK				   (0xff)
#define SCON2_RI2_MASK			   (0x01)
#define SCON2_RI2_POS			   (0)
#define SCON2_TI2_MASK			   (0x02)
#define SCON2_TI2_POS			   (1)
#define SCON2_RB82_MASK			   (0x04)
#define SCON2_RB82_POS			   (2)
#define SCON2_TB82_MASK			   (0x08)
#define SCON2_TB82_POS			   (3)
#define SCON2_REN2_MASK			   (0x10)
#define SCON2_REN2_POS			   (4)
#define SCON2_SM2S_MASK			   (0x80)
#define SCON2_SM2S_POS			   (7)

/*--------SBUF2---------------*/
#define SBUF2_MASK				   (0xff)
#define SBUF2_SBUF2_MASK		   (0xff)
#define SBUF2_SBUF2_POS			   (0)


/*--------OPTION--------------*/
#define OPTION_MASK				   (0xff)
#define OPTION_TM3PSC_MASK		   (0X03)
#define OPTION_TM3PSC_POS		   (0)
#define OPTION_ADCKS_MASK	       (0x0C)
#define OPTION_ADCKS_POS		   (2)
#define OPTION_WDTPSC_MASK         (0X30)
#define OPTION_WDTPSC_POS          (4)
#define OPTION_TM3CKS_MASK         (0XC0)
#define OPTION_TM3CKS_POS          (6)

/*--------INTFLG--------------*/
#define INTFLG_MASK				   (0xff)
#define INTFLG_TF3_MASK			   (0x01)
#define INTFLG_TF3_POS			   (0)
#define INTFLG_PCIF_MASK		   (0x02)
#define INTFLG_PCIF_POS			   (1)
#define INTFLG_ADIF_MASK		   (0x10)
#define INTFLG_ADIF_POS			   (4)
#define INTFLG_TKIFA_MASK		   (0x20)
#define INTFLG_TKIFA_POS		   (5)
#define INTFLG_LVDIF_MASK		   (0x80)
#define INTFLG_LVDIF_POS		   (7)


/*--------INTFLN--------------*/
#define INTPIN_MASK				   (0xff)
#define INTPIN_PIN0IF_MASK		   (0x01)
#define INTPIN_PIN0IF_POS		   (0)
#define INTPIN_PIN1IF_MASK		   (0x02)
#define INTPIN_PIN1IF_POS		   (1)
#define INTPIN_PIN2IF_MASK		   (0x04)
#define INTPIN_PIN2IF_POS		   (2)
#define INTPIN_PIN3IF_MASK		   (0x08)
#define INTPIN_PIN3IF_POS		   (3)
#define INTPIN_PIN4IF_MASK		   (0x10)
#define INTPIN_PIN4IF_POS		   (4)
#define INTPIN_PIN5IF_MASK		   (0x20)
#define INTPIN_PIN5IF_POS		   (5)
#define INTPIN_PIN6IF_MASK		   (0x40)
#define INTPIN_PIN6IF_POS		   (6)
#define INTPIN_PIN7IF_MASK		   (0x80)
#define INTPIN_PIN7IF_POS		   (7)


/*--------SWCMD---------------*/
#define SWCMD_MASK				   (0xff)
#define SWCMD_IAPEN_MASK		   (0x01)
#define SWCMD_IAPEN_POS			   (0)
#define SWCMD_WDTO_MASK		       (0x02)
#define SWCMD_WDTO_POS			   (1)
#define SWCMD_IAPEN_MASK		   (0x01)
#define SWCMD_IAPEN_POS			   (0)
#define SWCMD_SWRST_MASK		   (0x01)
#define SWCMD_SWRST_POS			   (0)


/*--------SCON---------------*/
#define SCON_MASK				   (0xff)
#define SCON_RI_MASK			   (0x01)
#define SCON_RI_POS			       (0)
#define SCON_TI_MASK			   (0x02)
#define SCON_TI_POS			       (1)
#define SCON_RB8_MASK			   (0x04)
#define SCON_RB8_POS		       (2)
#define SCON_TB8_MASK			   (0x08)
#define SCON_TB8_POS		       (3)
#define SCON_REN_MASK			   (0x10)
#define SCON_REN_POS		       (4)
#define SCON_SM2_MASK			   (0x20)
#define SCON_SM2_POS		       (5)
#define SCON_SM1_MASK              (0X40)
#define SCON_SM1_POS               (6)
#define SCON_SM0_MASK              (0X80)
#define SCON_SM0_POS               (7)

/*--------SCON1---------------*/
#define SCON1_MASK				   (0xff)
#define SCON1_RI1_MASK			   (0x01)
#define SCON1_RI1_POS			       (0)
#define SCON1_TI1_MASK			   (0x02)
#define SCON1_TI1_POS			       (1)
#define SCON1_RB81_MASK			   (0x04)
#define SCON1_RB81_POS		       (2)
#define SCON1_TB81_MASK			   (0x08)
#define SCON1_TB81_POS		       (3)
#define SCON1_REN1_MASK			   (0x10)
#define SCON1_REN1_POS		       (4)
#define SCON1_SM1S_MASK			   (0x80)
#define SCON1_SM1S_POS		       (7)

/*--------SBUF1---------------*/
#define SBUF_MASK				   (0xff)
#define SBUF_SBUF_MASK			   (0xff)
#define SBUF_SBUF_POS			   (0)

/*--------TKCON3---------------*/
#define TKCON3_MASK				   (0xff)
#define TKCON3_SPREAD_MASK			   (0x01)
#define TKCON3_SPREAD_POS			   (0)
#define TKCON3_JMPVALB_MASK			   (0x0E)
#define TKCON3_JMPVALB_POS			   (1)
#define TKCON3_TKXCAPB_MASK			   (0x10)
#define TKCON3_TKXCAPB_POS			   (4)
#define TKCON3_TKIFB_MASK			   (0x20)
#define TKCON3_TKIFB_POS			   (5)
#define TKCON3_TKEOCB_MASK			   (0x30)
#define TKCON3_TKEOCB_POS			   (6)
#define TKCON3_TKPDB_MASK			   (0x40)
#define TKCON3_TKPDB_POS			   (7)

/*--------PWM2CON---------------*/
#define PWMCON2_MASK 			   (0XFF)
#define PWMCON2_PWM2DZ_MASK  	   (0X3F)
#define PWMCON2_PWM2DZ_POS   	   (0)
#define PWMCON2_PWM2OM_MASK        (0XC0)
#define PWMCON2_PWM2OM_POS         (6)

/*--------PWMIDX---------------*/
#define PWMIDX_MASK 			   (0XFF)
#define PWMIDX_PWMIDX_MASK  	   (0XFF)
#define PWMIDX_PWMIDX_POS   	   (0)

/*--------PWMEN--------------*/
#define PWMEN_MASK				   (0xff)
#define PWMEN_PWM0EN_MASK		   (0x01)
#define PWMEN_PWM0EN_POS		   (0)
#define PWMEN_PWM1EN_MASK		   (0x02)
#define PWMEN_PWM1EN_POS		   (1)
#define PWMEN_PWM2EN_MASK		   (0x04)
#define PWMEN_PWM2EN_POS		   (2)
#define PWMEN_PWM3EN_MASK		   (0x08)
#define PWMEN_PWM3EN_POS		   (3)
#define PWMEN_PWM0IE_MASK		   (0x10)
#define PWMEN_PWM0IE_POS		   (4)
#define PWMEN_PWM1IE_MASK		   (0x20)
#define PWMEN_PWM1IE_POS		   (5)
#define PWMEN_PWM2IE_MASK		   (0x40)
#define PWMEN_PWM2IE_POS		   (6)
#define PWMEN_PWM3IE_MASK		   (0x80)
#define PWMEN_PWM3IE_POS		   (7)


/*--------PWMCON---------------*/
#define PWMCON_MASK 			   (0XFF)
#define PWMCON_PWM0CKS_MASK  	   (0X03)
#define PWMCON_PWM0CKS_POS   	   (0)
#define PWMCON_PWM1CKS_MASK        (0X0C)
#define PWMCON_PWM1CKS_POS         (2)
#define PWMCON_PWM2CKS_MASK        (0X30)
#define	PWMCON_PWM2CKS_POS         (4)
#define PWMCON_PWM3CKS_MASK        (0XC0)
#define	PWMCON_PWM3CKS_POS         (6)

/*--------PINMOD10---------------*/
#define PINMOD10_MASK 		       (0XFF)
#define PINMOD10_PINMOD0_MASK  	   (0X0F)
#define PINMOD10_PINMOD0_POS   	   (0)
#define PINMOD10_PINMOD1_MASK      (0XF0)
#define PINMOD10_PINMOD1_POS       (4)


/*--------PINMOD32---------------*/
#define PINMOD32_MASK 			   (0XFF)
#define PINMOD32_PINMOD2_MASK  	   (0X0F)
#define PINMOD32_PINMOD2_POS   	   (0)
#define PINMOD32_PINMOD3_MASK      (0XF0)
#define PINMOD32_PINMOD3_POS       (4)


/*--------PINMOD54---------------*/
#define PINMOD54_MASK 			   (0XFF)
#define PINMOD54_PINMOD4_MASK  	   (0X0F)
#define PINMOD54_PINMOD4_POS   	   (0)
#define PINMOD54_PINMOD5_MASK      (0XF0)
#define PINMOD54_PINMOD5_POS       (4)


/*--------PINMOD76---------------*/
#define PINMOD76_MASK 			   (0XFF)
#define PINMOD76_PINMOD6_MASK  	   (0X0F)
#define PINMOD76_PINMOD6_POS   	   (0)
#define PINMOD76_PINMOD7_MASK      (0XF0)
#define PINMOD76_PINMOD7_POS       (4)

/*--------PINMODE---------------*/
#define PINMODE_MASK 			   (0XFF)
#define PINMODE_UART0PS_MASK  	   (0X03)
#define PINMODE_UART0PS_POS   	   (0)
#define PINMODE_I2CPS_MASK        (0X0C)
#define PINMODE_I2CPS_POS         (2)
#define PINMODE_PSEUDOEN_MASK          (0X10)
#define PINMODE_PSEUDOEN_POS           (4)
#define PINMODE_UART1PS_MASK        (0X20)
#define PINMODE_UART1PS_POS         (5)
#define PINMODE_VBGEN_MASK        (0X80)
#define PINMODE_VBGEN_POS         (7)

/*--------TKCHSA---------------*/
#define TKCHSA_MASK				   (0x1F)
#define TKCHSA_TKCHSA_MASK			   (0x1f)
#define TKCHSA_TKCHSA_POS			   (0)

/*--------IE---------------*/
#define IE_MASK					   (0xff)
#define IE_EX0_MASK  		       (0x01)    
#define IE_EX0_POS			       (0)
#define IE_ET0_MASK  		       (0x02)    
#define IE_ET0_POS			       (1)
#define IE_EX1_MASK  		       (0x04)    
#define IE_EX1_POS			       (2)
#define IE_ET1_MASK  		       (0x08)    
#define IE_ET1_POS			       (3)
#define IE_ES_MASK  		       (0x10)    
#define IE_ES_POS			       (4)
#define IE_ET2_MASK  		       (0x20)    
#define IE_ET2_POS			       (5)
#define IE_EA_MASK  		       (0x80)    
#define IE_EA_POS			       (7)

/*--------INTE1---------------*/
#define INTE1_MASK                 (0XFF)
#define INTE1_TM3IE_MASK           (0X01)
#define INTE1_TM3IE_POS            (0)
#define INTE1_PCIE_MASK            (0X02)
#define INTE1_PCIE_POS             (1)
#define INTE1_LVDIE_MASK           (0X04)
#define INTE1_LVDIE_POS            (2)
#define INTE1_ADTKIE_MASK            (0X08)
#define INTE1_ADTKIE_POS             (3)
#define INTE1_SPIE_MASK            (0X10)
#define INTE1_SPIE_POS             (4)
#define INTE1_ES2_MASK             (0X20)
#define INTE1_ES2_POS              (5)
#define INTE1_I2CE_MASK            (0X40)
#define INTE1_I2CE_POS             (6)
#define INTE1_PWMIE_MASK           (0X80)
#define INTE1_PWMIE_POS            (7)


/*--------ADCDL   ADCDH---------------*/
#define ADCDL_MASK                 (0XFF)
#define ADCDL_ADCDL_MASK           (0XF0)
#define ADCDL_ADCDL_POS            (0)

#define ADCDH_MASK                 (0XFF)
#define ADCDH_ADCDH_MASK           (     0XFF)
#define ADCDH_ADCDH_POS            (0)


/*--------TKCHSB---------------*/
#define TKCHSB_MASK                 (0X1F)
#define TKCHSB_TKCHSB_MASK           (0X1F)
#define TKCHSB_TKCHSB_POS            (0)


/*--------TKCON---------------*/
#define TKCON_MASK                 (0XFF)
#define TKCON_ATKMODE_MASK           (0X03)
#define TKCON_ATKMODE_POS            (0)
#define TKCON_TKOFFSET_MASK            (0X04)
#define TKCON_TKOFFSET_POS             (2)
#define TKCON_TKXCAPA_MASK           (0X08)
#define TKCON_TKXCAPA_POS            (3)
#define TKCON_TKIVCS_MASK            (0X10)
#define TKCON_TKIVCS_POS             (4)
#define TKCON_TKRERUN_MASK            (0X20)
#define TKCON_TKRERUN_POS             (5)
#define TKCON_TKEOCA_MASK             (0X40)
#define TKCON_TKEOCA_POS              (6)
#define TKCON_TKPDA_MASK            (0X80)
#define TKCON_TKPDA_POS             (7)

/*--------CHSEL---------------*/
#define CHSEL_MASK                 (0XfF)
#define CHSEL_ADCHS_MASK           (0X3F)
#define CHSEL_ADCHS_POS            (0)
#define CHSEL_ADCVREFS_MASK           (0XC0)
#define CHSEL_ADCVREFS_POS            (6)

/*--------ATKCHB2---------------*/
#define ATKCHB2_MASK                 (0XFF)
#define ATKCHB2_ATKCHB2_MASK           (0XFF)
#define ATKCHB2_ATKCHB2_POS            (0)

/*--------LXDCON---------------*/
#define LXDCON_MASK                 (0XFF)
#define LXDCON_LXDBRIT_MASK           (0X07)
#define LXDCON_LXDBRIT_POS            (0)
#define LXDCON_LEDBRITM_MASK            (0X08)
#define LXDCON_LEDBRITM_POS             (3)
#define LXDCON_LXDDUTY_MASK           (0X70)
#define LXDCON_LXDDUTY_POS            (4)
#define LXDCON_LXDON_MASK            (0X80)
#define LXDCON_LXDON_POS             (7)

/*--------LXDCON2---------------*/
#define LXDCON2_MASK                 (0XFF)
#define LXDCON2_LEDMODE_MASK           (0X03)
#define LXDCON2_LEDMODE_POS            (0)
#define LXDCON2_LEDHOLD_MASK            (0X08)
#define LXDCON2_LEDHOLD_POS             (3)
#define LXDCON2_SELLED_MASK           (0X10)
#define LXDCON2_SELLED_POS            (4)
#define LXDCON2_LXDPSC_MASK           (0X60)
#define LXDCON2_LXDPSC_POS            (5)
#define LXDCON2_LCDCKS_MASK            (0X80)
#define LXDCON2_LCDCKS_POS             (7)

/*--------TKTMRL---------------*/
#define TKTMRL_MASK                 (0XFF)
#define TKTMRL_TKTMRL_MASK           (0XFF)
#define TKTMRL_TKTMRL_POS            (0)

/*--------TKCON2---------------*/
#define TKCON2_MASK                 (0XFF)
#define TKCON2_TKTMRH_MASK           (0X0F)
#define TKCON2_TKTMRH_POS            (0)
#define TKCON2_JMPVALA_MASK            (0X70)
#define TKCON2_JMPVALA_POS             (4)
#define TKCON2_TKFJMP_MASK           (0X80)
#define TKCON2_TKFJMP_POS            (7)


/*--------ATKCHB1   ATKCHB0---------------*/
#define ATKCHB1_MASK                 (0XFF)
#define AATKCHB1_ATKCHB1_MASK           (0XFF)
#define ATKCHB1_ATKCHB1_POS            (0)

#define ATKCHB0_MASK                 (0XFF)
#define ATKCHB0_ATKCHB0_MASK           (0XFF)
#define ATKCHB0_ATKCHB0_POS            (0)

/*--------IP---------------*/
#define IP_MASK                    (0XFF)
#define IP_PX0_MASK                (0X01)
#define IP_PX0_POS                 (0)
#define IP_PT0_MASK                (0X02)
#define IP_PT0_POS                 (1)
#define IP_PX1_MASK                (0X04)
#define IP_PX1_POS                 (2)
#define IP_PT1_MASK                (0X08)
#define IP_PT1_POS                 (3)
#define IP_PS_MASK                 (0X10)
#define IP_PS_POS                  (4)
#define IP_PT2_MASK                (0X20)
#define IP_PT2_POS                 (5)



/*--------IPH---------------*/
#define IPH_MASK                   (0XFF)
#define IPH_PX0H_MASK              (0X01)
#define IPH_PX0H_POS               (0)
#define IPH_PT0H_MASK              (0X02)
#define IPH_PT0H_POS               (1)
#define IPH_PX1H_MASK              (0X04)
#define IPH_PX1H_POS               (2)
#define IPH_PT1H_MASK              (0X08)
#define IPH_PT1H_POS               (3)
#define IPH_PSH_MASK               (0X10)
#define IPH_PSH_POS                (4)
#define IPH_PT2H_MASK              (0X20)
#define IPH_PT2H_POS               (5)


#define IP1_MASK                   (0XFF)
#define IP1_PT3_MASK               (0X01)
#define IP1_PT3_POS                (0)
#define IP1_PPC_MASK               (0X02)
#define IP1_PPC_POS                (1)
#define IP1_PLVD_MASK              (0X04)
#define IP1_PLVD_POS               (2)
#define IP1_PADTKI_MASK              (0X08)
#define IP1_PADTKI_POS               (3)
#define IP1_PSPI_MASK              (0X10)
#define IP1_PSPI_POS               (4)
#define IP1_PS2_MASK               (0X20)
#define IP1_PS2_POS                (5)
#define IP1_PI2C_MASK              (0X40)
#define IP1_PI2C_POS               (6)
#define IP1_PPWM_MASK              (0X80)
#define IP1_PPWM_POS               (7)

/*--------IP1H---------------*/
#define IP1H_MASK                  (0XFF)
#define IP1H_PT3H_MASK             (0X01)
#define IP1H_PT3H_POS              (0)
#define IP1H_PPCH_MASK             (0X02)
#define IP1H_PPCH_POS              (1)
#define IP1H_PLVDH_MASK            (0X04)
#define IP1H_PLVDH_POS             (2)
#define IP1H_PADTKIH_MASK            (0X08)
#define IP1H_PADTKIH_POS             (3)
#define IP1H_PSPIH_MASK              (0X10)
#define IP1H_PSPIH_POS               (4)
#define IP1H_PS2H_MASK             (0X20)
#define IP1H_PS2H_POS              (5)
#define IP1H_PI2CH_MASK            (0X40)
#define IP1H_PI2CH_POS             (6)
#define IP1H_PPWMH_MASK            (0X80)
#define IP1H_PPWMH_POS             (7)

/*--------SPCON---------------*/
#define SPCON_MASK                  (0XFF)
#define SPCON_SPCR_MASK             (0X03)
#define SPCON_SPCR_POS              (0)
#define SPCON_LSBF_MASK             (0X04)
#define SPCON_LSBF_POS              (2)
#define SPCON_SSDIS_MASK            (0X08)
#define SPCON_SSDIS_POS             (3)
#define SPCON_CPHA_MASK            (0X10)
#define SPCON_CPHA_POS             (4)
#define SPCON_CPOL_MASK              (0X20)
#define SPCON_CPOL_POS               (5)
#define SPCON_MSTR_MASK             (0X40)
#define SPCON_MSTR_POS              (6)
#define SPCON_SPEN_MASK            (0X80)
#define SPCON_SPEN_POS             (7)

/*--------SPSTA---------------*/
#define SPSTA_MASK                  (0XFF)
#define SPSTA_SPBSY_MASK             (0X04)
#define SPSTA_SPBSY_POS              (2)
#define SPSTA_RCVBF_MASK             (0X08)
#define SPSTA_RCVBF_POS              (3)
#define SPSTA_RCVOVF_MASK            (0X10)
#define SPSTA_RCVOVF_POS             (4)
#define SPSTA_MODF_MASK            (0X20)
#define SPSTA_MODF_POS             (5)
#define SPSTA_WCOL_MASK              (0X40)
#define SPSTA_WCOL_POS               (6)
#define SPSTA_SPIF_MASK             (0X80)
#define SPSTA_SPIF_POS              (7)

/*--------SPDAT---------------*/
#define SPDAT_MASK                 (0XFF)
#define SPDAT_SPDAT_MASK           (0XFF)
#define SPDAT_SPDAT_POS            (0)

/*--------LVDCON---------------*/
#define LVDCON_MASK                  (0XFF)
#define LVDCON_LVDS_MASK             (0X0f)
#define LVDCON_LVDS_POS              (0)
#define LVDCON_LVDPD_MASK             (0X10)
#define LVDCON_LVDPD_POS              (4)
#define LVDCON_LVDDBS_MASK            (0X20)
#define LVDCON_LVDDBS_POS             (5)
#define LVDCON_LVDO_MASK            (0X40)
#define LVDCON_LVDO_POS             (6)
#define LVDCON_LVDM_MASK              (0X80)
#define LVDCON_LVDM_POS               (7)

/*--------TKPINSA0---------------*/
#define TKPINSA0_MASK                 (0XFF)
#define TKPINSA0_TKPINSA0_MASK           (0XFF)
#define TKPINSA0_TKPINSA0_POS            (0)

/*--------TKPINSA1---------------*/
#define TKPINSA1_MASK                 (0XFF)
#define TKPINSA1_TKPINSA1_MASK           (0XFF)
#define TKPINSA1_TKPINSA1_POS            (0)

/*--------TKPINSA2---------------*/
#define TKPINSA2_MASK                 (0XFF)
#define TKPINSA2_TKPINSA2_MASK           (0XFF)
#define TKPINSA2_TKPINSA2_POS            (0)

/*--------TKPINSB0---------------*/
#define TKPINSB0_MASK                 (0XFF)
#define TKPINSB0_TKPINSB0_MASK           (0XFF)
#define TKPINSB0_TKPINSB0_POS            (0)

/*--------ATKCHA0---------------*/
#define ATKCHA0_MASK                 (0XFF)
#define ATKCHA0_ATKCHA0_MASK           (0XFF)
#define ATKCHA0_ATKCHA0_POS            (0)

/*--------ATKCHA1---------------*/
#define ATKCHA1_MASK                 (0XFF)
#define ATKCHA1_ATKCHA1_MASK           (0XFF)
#define ATKCHA1_ATKCHA1_POS            (0)

/*--------ATKCHA2---------------*/
#define ATKCHA2_MASK                 (0XFF)
#define ATKCHA2_ATKCHA2_MASK           (0XFF)
#define ATKCHA2_ATKCHA2_POS            (0)
      
/*--------T2CON---------------*/
#define T2CON_MASK                  (0XFF)
#define T2CON_CPRL2N_MASK             (0X01)
#define T2CON_CPRL2N_POS              (0)
#define T2CON_CT2N_MASK             (0X02)
#define T2CON_CT2N_POS              (1)
#define T2CON_TR2_MASK            (0X04)
#define T2CON_TR2_POS             (2)
#define T2CON_EXEN2_MASK            (0X08)
#define T2CON_EXEN2_POS             (3)
#define T2CON_TCLK_MASK              (0X10)
#define T2CON_TCLK_POS               (4)
#define T2CON_RCLK_MASK             (0X20)
#define T2CON_RCLK_POS              (5)
#define T2CON_EXF2_MASK            (0X40)
#define T2CON_EXF2_POS             (6)
#define T2CON_TF2_MASK            (0X80)
#define T2CON_TF2_POS             (7)


/*--------IAPWE---------------*/
#define IAPWE_MASK                  (0XFF)

/*--------RCP2L---------------*/
#define RCP2L_MASK                 (0XFF)
#define RCP2L_RCP2L_MASK           (0XFF)
#define RCP2L_RCP2L_POS            (0)

/*--------RCP2H---------------*/
#define RCP2H_MASK                 (0XFF)
#define RCP2H_RCP2H_MASK           (0XFF)
#define RCP2H_RCP2H_POS            (0)

/*--------TL2---------------*/
#define TL2_MASK                 (0XFF)
#define TL2_TL2_MASK           (0XFF)
#define TL2_TL2_POS            (0)

/*--------TH2---------------*/
#define TH2_MASK                 (0XFF)
#define TH2_TH2_MASK           (0XFF)
#define TH2_TH2_POS            (0)

/*--------EXA2---------------*/
#define EXA2_MASK                 (0XFF)
#define EXA2_EXA2_MASK           (0XFF)
#define EXA2_EXA2_POS            (0)

/*--------EXA3---------------*/
#define EXA3_MASK                 (0XFF)
#define EXA3_EXA3_MASK           (0XFF)
#define EXA3_EXA3_POS            (0)

/*--------PSW---------------*/
#define PSW_MASK                  (0XFF)
#define PSW_P_MASK             (0X01)
#define PSW_P_POS              (0)
#define PSW_F1_MASK             (0X02)
#define PSW_F1_POS              (1)
#define PSW_OV_MASK            (0X04)
#define PSW_OV_POS             (2)
#define PSW_RS0_MASK            (0X08)
#define PSW_RS0_POS             (3)
#define PSW_RS1_MASK              (0X10)
#define PSW_RS1_POS               (4)
#define PSW_F0_MASK             (0X20)
#define PSW_F0_POS              (5)
#define PSW_AC_MASK            (0X40)
#define PSW_AC_POS             (6)
#define PSW_CY_MASK            (0X80)
#define PSW_CY_POS             (7)

/*--------PWMDH---------------*/
#define PWMDH_MASK                 (0XFF)
#define PWMDH_PWMDH_MASK           (0XFF)
#define PWMDH_PWMDH_POS            (0)

/*--------PWMDL---------------*/
#define PWMDL_MASK                 (0XFF)
#define PWMDL_PWMDL_MASK           (0XFF)
#define PWMDL_PWMDL_POS            (0)

/*--------LVRCON---------------*/
#define LVRCON_MASK                  (0XFF)
#define LVRCON_LVRS_MASK             (0X07)
#define LVRCON_LVRS_POS              (0)
#define LVRCON_LVRPD_MASK             (0X10)
#define LVRCON_LVRPD_POS              (4)
#define LVRCON_SXTGAIN_MASK            (0XC0)
#define LVRCON_SXTGAIN_POS             (6)

/*--------TKPINSB1---------------*/
#define TKPINSB1_MASK                 (0XFF)
#define TKPINSB1_TKPINSB1_MASK           (0XFF)
#define TKPINSB1_TKPINSB1_POS            (0)

/*--------CLKCON---------------*/
#define CLKCON_MASK                (0XFF)
#define CLKCON_CLKPSC_MASK         (0X03)
#define CLKCON_CLKPSC_POS          (0)
#define CLKCON_SELFCK_MASK         (0X04)
#define CLKCON_SELFCK_POS          (2)
#define CLKCON_STPFCK_MASK         (0X08)
#define CLKCON_STPFCK_POS          (3)
#define CLKCON_STPPCK_MASK         (0X10)
#define CLKCON_STPPCK_POS          (4)
#define CLKCON_STPSCK_MASK         (0X20)
#define CLKCON_STPSCK_POS          (5)
#define CLKCON_FCKTYPE_MASK        (0X40)
#define CLKCON_FCKTYPE_POS         (6)
#define CLKCON_SCKTYPE_MASK        (0X80)
#define CLKCON_SCKTYPE_POS         (7)

/*--------PWMPRDH---------------*/
#define PWMPRDH_MASK                 (0XFF)
#define PWMPRDH_PPWMPRDH_MASK           (0XFF)
#define PWMPRDH_PWMPRDH_POS            (0)

/*--------PWMPRDL---------------*/
#define PWMPRDL_MASK                 (0XFF)
#define PWMPRDL_PWMPRDL_MASK           (0XFF)
#define PWMPRDL_PWMPRDL_POS            (0)

/*--------UART2CON---------------*/
#define UART2CON_MASK                 (0X7F)
#define UART2CON_UART2BRP_MASK           (0X7F)
#define UART2CON_UART2BRP_POS            (0)

/*--------UART1CON---------------*/
#define UART1CON_MASK                 (0X7F)
#define UART1CON_UART1BRP_MASK           (0X7F)
#define UART1CON_UART1BRP_POS            (0)

/*--------UART0CON---------------*/
#define UART0CON_MASK                  (0XFF)
#define UART0CON_UART0BRP_MASK             (0X7F)
#define UART0CON_UART0BRP_POS              (0)
#define UART0CON_UART0BRS_MASK             (0X80)
#define UART0CON_UART0BRS_POS              (7)

/*--------TKPINSB2---------------*/
#define TKPINSB2_MASK                 (0XFF)
#define TKPINSB2_TKPINSB2_MASK           (0XFF)
#define TKPINSB2_TKPINSB2_POS            (0)


/*--------ACC---------------*/

/*--------MICON---------------*/
#define MICON_MASK                 (0XFF)
#define MICON_MICR_MASK            (0X03)
#define MICON_MICR_POS             (0)
#define MICON_MISTOP_MASK          (0X04)
#define MICON_MISTOP_POS           (2)
#define MICON_MISTART_MASK         (0X08)
#define MICON_MISTART_POS          (3)
#define MICON_MIACKI_MASK          (0X10)
#define MICON_MIACKI_POS           (4)
#define MICON_MIIF_MASK            (0X20)
#define MICON_MIIF_POS             (5)
#define MICON_MIACKO_MASK          (0X40)
#define MICON_MIACKO_POS           (6)
#define MICON_MIEN_MASK            (0X80)
#define MICON_MIEN_POS             (7)

/*--------MIDAT---------------*/
#define MIDAT_MASK                 (0XFF)
#define MIDAT_MIDAT_MASK           (0XFF)
#define MIDAT_MIDAT_POS            (0)


/*--------EFTCON---------------*/
#define EFTCON_MASK                 (0XFF)
#define EFTCON_CKHLDE_MASK            (0X01)
#define EFTCON_CKHLDE_POS             (0)
#define EFTCON_EFTWOUT_MASK          (0X02)
#define EFTCON_EFTWOUT_POS           (1)
#define EFTCON_EFTWCPU_MASK         (0X04)
#define EFTCON_EFTWCPU_POS          (2)
#define EFTCON_EFTSLOW_MASK          (0X08)
#define EFTCON_EFTSLOW_POS           (3)
#define EFTCON_EFT1S_MASK            (0X30)
#define EFTCON_EFT1S_POS             (4)
#define EFTCON_EFT1CS_MASK          (0X40)
#define EFTCON_EFT1CS_POS           (6)
#define EFTCON_EFT2CS_MASK            (0X80)
#define EFTCON_EFT2CS_POS             (7)


/*--------EXA---------------*/
#define EXA_MASK                 (0XFF)
#define EXA_EXA_MASK           (0XFF)
#define EXA_EXA_POS            (0)

/*--------EXA---------------*/
#define EXA_MASK                 (0XFF)
#define EXA_EXA_MASK           (0XFF)
#define EXA_EXA_POS            (0)

/*--------SIADR---------------*/
#define SIADR_MASK                 (0XFF)
#define SIADR_SIEN_MASK           (0X01)
#define SIADR_SIEN_POS            (0)
#define SIADR_SA_MASK           (0XFE)
#define SIADR_SA_POS            (1)

/*--------SICON---------------*/
#define SICON_MASK                  (0XFF)
#define SICON_RCD1F_MASK             (0X01)
#define SICON_RCD1F_POS              (0)
#define SICON_RCD2F_MASK             (0X02)
#define SICON_RCD2F_POS              (1)
#define SICON_TXDF_MASK            (0X04)
#define SICON_TXDF_POS             (2)
#define SICON_RCD1IE_MASK              (0X10)
#define SICON_RCD1IE_POS               (4)
#define SICON_RCD2IE_MASK             (0X20)
#define SICON_RCD2IE_POS              (5)
#define SICON_TXDIE_MASK            (0X40)
#define SICON_TXDIE_POS             (6)
#define SICON_MIIE_MASK            (0X80)
#define SICON_MIIE_POS             (7)

/*--------SIRCD1---------------*/
#define SIRCD1_MASK                 (0XFF)
#define SIRCD1_SIRCD1_MASK           (0XFF)
#define SIRCD1_SIRCD1_POS            (0)

/*--------SITXRCD2---------------*/
#define SITXRCD2_MASK                 (0XFF)
#define SITXRCD2_SITXRCD2_MASK           (0XFF)
#define SITXRCD2_SITXRCD2_POS            (0)

/*--------BOOTV---------------*/
#define BOOTV_MASK                  (0XFF)
#define BOOTV_BOOTVVR_MASK             (0X03)
#define BOOTV_BOOTVR_POS              (0)
#define BOOTV_RSTV_MASK             (0X04)
#define BOOTV_RSTV_POS              (2)


/*--------PWRCON---------------*/
#define PWRCON_MASK                  (0XFF)
#define PWRCON_PWRSLOW_MASK             (0X01)
#define PWRCON_PWRSLOW_POS              (0)
#define PWRCON_PWRIDLE_MASK             (0X02)
#define PWRCON_PWRIDLE_POS              (1)
#define PWRCON_ENVPULL_MASK            (0X04)
#define PWRCON_ENVPULL_POS             (2)
#define PWRCON_WARMTIME_MASK            (0X08)
#define PWRCON_WARMTIME_POS             (3)
#define PWRCON_AVPULL_MASK              (0X10)
#define PWRCON_AVPULL_POS               (4)


/*--------CRCDL---------------*/
#define CRCDL_MASK                 (0XFF)
#define CRCDL_CRCDL_MASK           (0XFF)
#define CRCDL_CRCDL_POS            (0)

/*--------CRCDH---------------*/
#define CRCDH_MASK                 (0XFF)
#define CRCDH_CRCDH_MASK           (0XFF)
#define CRCDH_CRCDH_POS            (0)

/*--------CRCIN---------------*/
#define CRCIN_MASK                 (0XFF)
#define CRCIN_CRCIN_MASK           (0XFF)
#define CRCIN_CRCIN_POS            (0)

/*--------CFGBG---------------*/
#define CFGBG_MASK                 (0X1F)
#define CFGBG_CFGBG_MASK           (0X1F)
#define CFGBG_CFGBG_POS            (0)

/*--------CFGWL---------------*/
#define CFGWL_MASK                 (0X7F)
#define CFGWL_CFGWL_MASK           (0X7F)
#define CFGWL_CFGWL_POS            (0)


/*--------AUX2---------------*/
#define AUX2_MASK                  (0XFF)
#define AUX2_MULDIV16_MASK         (0X01)
#define AUX2_MULDIV16_POS          (0)
#define AUX2_IAPTE_MASK            (0X06)
#define AUX2_IAPTE_POS             (1)
#define AUX2_DIV32_MASK            (0X08)
#define AUX2_DIV32_POS             (3)
#define AUX2_VBGOUT_MASK           (0X10)
#define AUX2_VBGOUT_POS            (4)
#define AUX2_PWRSAV_MASK           (0X20)
#define AUX2_PWRSAV_POS            (5)
#define AUX2_WDTE_MASK             (0XC0)
#define AUX2_WDTE_POS              (6)

/*--------AUX1---------------*/
#define AUX1_MASK            	   (0XFF)
#define AUX1_DPSEL_MASK            (0X01)
#define AUX1_DPSEL_POS             (0)
#define AUX1_T1SEL_MASK            (0X02)
#define AUX1_T1SEL_POS             (1)
#define AUX1_TKSOCB_MASK            (0X04)
#define AUX1_TKSOCB_POS             (2)
#define AUX1_ADSOC_MASK            (0X10)
#define AUX1_ADSOC_POS             (4)
#define AUX1_TKSOCA_MASK            (0X20)
#define AUX1_TKSOCA_POS             (4)
#define AUX1_CLRTM3_MASK           (0X40)
#define AUX1_CLRTM3_POS            (6)
#define AUX1_CLRWDT_MASK           (0X80)
#define AUX1_CLRWDT_POS            (7)

/*--------GPIO---------------*/
//配合PORTIDX使用，指明要配置的PORT口
#define  PORT0   0
#define  PORT1   1
#define  PORT2   2
#define  PORT3   3
#define  PORT4   4
#define  PORT5   5
 
#define		PIN_MODE_OD 	  	            0x01    //开漏
#define		PIN_MODE_OD_IPU	              0x00    //开漏带上拉
#define		PIN_MODE_OD_IPD	              0x04    //开漏带下拉

#define		PIN_MODE_PP		                0x06    //推挽输出
#define		PIN_MODE_ADC		              0x03    //ADC
#define		PIN_MODE_LED		              0x07    //LED
#define		PIN_MODE_AF 	                0x0B    //PWM TxO CKO
#define		PIN_MODE_COM		              0x0F    //LCD 1/2BIAS

#define		PIN_MODE_OD_WAKEUP 	  	      0x09    //开漏+唤醒
#define		PIN_MODE_OD_IPU_WAKEUP	      0x08    //开漏带上拉+唤醒
#define		PIN_MODE_OD_IPD_WAKEUP	      0x0C    //开漏带下拉+唤醒




//连续4个bit配置一个IO口
#define		PIN_L_MODE_OD 	  	        0x01    //开漏
#define		PIN_L_MODE_OD_IPU	          0x00    //开漏带上拉
#define		PIN_L_MODE_OD_IPD	          0x04    //开漏带下拉

#define		PIN_L_MODE_PP		            0x06    //推挽输出
#define		PIN_L_MODE_ADC		          0x03    //ADC
#define		PIN_L_MODE_LED		          0x07    //LED
#define		PIN_L_MODE_AF 		          0x0B    //PWM TxO CKO
#define		PIN_L_MODE_COM		          0x0F    //LCD 1/2BIAS

#define		PIN_L_MODE_OD_WAKEUP 	  	  0x09    //开漏+唤醒
#define		PIN_L_MODE_OD_IPU_WAKEUP	  0x08    //开漏带上拉+唤醒
#define		PIN_L_MODE_OD_IPD_WAKEUP	  0x0C    //开漏带下拉+唤醒

#define		PIN_H_MODE_OD 		          0x10    //开漏
#define		PIN_H_MODE_OD_IPU	          0x00    //开漏带上拉
#define		PIN_H_MODE_OD_IPD	          0x40    //开漏带下拉

#define		PIN_H_MODE_PP		  	        0x60    //推挽输出
#define		PIN_H_MODE_ADC		          0x30    //ADC
#define		PIN_H_MODE_LED		          0x70    //LED
#define		PIN_H_MODE_AF 		          0xB0    //PWM TxO CKO
#define		PIN_H_MODE_COM		          0xF0    //LCD 1/2BIAS

#define		PIN_H_MODE_OD_WAKEUP 		    0x90    //开漏+唤醒
#define		PIN_H_MODE_OD_IPU_WAKEUP	  0x80    //开漏带上拉+唤醒
#define		PIN_H_MODE_OD_IPD_WAKEUP	  0xC0    //开漏带下拉+唤醒


#define SET_REG_BITS(reg,regbit,value) ((reg) = (reg)&~(reg##_##regbit##_##MASK) | ((value) << (reg##_##regbit##_##POS)))               //设置多位寄存器


#define	CLEAR_REG_BITS(reg,regbit) ((reg) = (reg)&~(reg##_##regbit##_##MASK))                                                           //清除寄存器当前状态

#define SET_REG(reg,value)	((reg) = (value))                                                                                           //设置一位寄存器
#define GET_REG(reg)		(reg)

#endif





