C51 COMPILER V9.60.7.0   IIC                                                               07/26/2025 11:36:01 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE IIC
OBJECT MODULE PLACED IN .\Objects\iic.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\iic.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hardware
                    -) DEBUG OBJECTEXTEND PRINT(.\Listings\iic.lst) TABS(2) OBJECT(.\Objects\iic.obj)

line level    source

   1          #include "main.h"
   2          
   3          // p3_4 scl
   4          // p3_5 sda
   5          
   6          // p3_0 scl
   7          // p3_1 sda
   8          
   9          void iic_init(void)
  10          {
  11   1          SET_REG(PORTIDX, 3);
  12   1          SET_REG_BITS(PINMOD54, <PERSON><PERSON><PERSON>D4, <PERSON>IN_MODE_OD_IPU); // P34
  13   1          SET_REG_BITS(PINMOD54, <PERSON><PERSON><PERSON>D5, PIN_MODE_OD_IPU); // P35
  14   1          P3_4 = 1;
  15   1          P3_5 = 1;
  16   1      }
  17          
  18          void iic_delay(void)
  19          {
  20   1          uint8_t i;
  21   1          i = 0;
  22   1          while (i--)
  23   1              ;
  24   1      }
  25          
  26          void iic_start(void)
  27          {
  28   1          SetSDA1(1);
  29   1          iic_delay();
  30   1          SetSCL1(1);
  31   1          iic_delay();
  32   1          SetSDA1(0);
  33   1          iic_delay();
  34   1          SetSCL1(0);
  35   1      }
  36          
  37          void iic_stop(void)
  38          {
  39   1          SetSDA1(0);
  40   1          iic_delay();
  41   1          SetSCL1(1);
  42   1          iic_delay();
  43   1          SetSDA1(1);
  44   1      }
  45          
  46          void iic_ack(void)
  47          {
  48   1          SetSDA1(0);
  49   1          iic_delay();
  50   1          SetSCL1(1);
  51   1          iic_delay();
  52   1          SetSCL1(0);
  53   1          iic_delay();
  54   1      }
C51 COMPILER V9.60.7.0   IIC                                                               07/26/2025 11:36:01 PAGE 2   

  55          
  56          void iic_nack(void)
  57          {
  58   1          SetSDA1(1);
  59   1          iic_delay();
  60   1          SetSCL1(1);
  61   1          iic_delay();
  62   1          SetSCL1(0);
  63   1      }
  64          
  65          unsigned char iic_recack(void)
  66          {
  67   1          unsigned char AckBit;
  68   1          SetSDA1(1);
  69   1          SetSCL1(1);
  70   1          iic_delay();
  71   1          AckBit = SDA1;
  72   1          SetSCL1(0);
  73   1          return AckBit;
  74   1      }
  75          
  76          void iic_write_byte(unsigned char iic_data)
  77          {
  78   1          uint8_t i;
  79   1          for (i = 0; i < 8; i++)
  80   1          {
  81   2              SetSCL1(0);
  82   2              iic_data <<= 1;
  83   2              if (CY)
  84   2                  SetSDA1(1);
  85   2              else
  86   2                  SetSDA1(0);
  87   2              iic_delay();
  88   2              SetSCL1(1);
  89   2              iic_delay();
  90   2          }
  91   1          SetSCL1(0);
  92   1      }
  93          
  94          unsigned char iic_read_byte(void)
  95          {
  96   1          uint8_t i;
  97   1          uint8_t iic_data;
  98   1          SetSCL1(0);
  99   1          SetSDA1(1);
 100   1          for (i = 0; i < 8; i++)
 101   1          {
 102   2              SetSCL1(1);
 103   2              iic_delay();
 104   2              iic_data <<= 1;
 105   2              if (SDA1)
 106   2                  iic_data |= 0x01;
 107   2              SetSCL1(0);
 108   2              iic_delay();
 109   2          }
 110   1          SetSDA1(1);
 111   1          return iic_data;
 112   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    146    ----
C51 COMPILER V9.60.7.0   IIC                                                               07/26/2025 11:36:01 PAGE 3   

   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
