#include "main.h"

uint8_t scan_switch_time = 0; // 扫描开关时间
uint8_t alarm_state = 0;      // 闹钟状态
uint8_t snooze_state = 0;     // 贪睡状态
uint8_t alarm_set_flag = 0;   // 闹钟设置标志

static uint8_t last_alarm_key_state = 1; // 记录上一次闹钟开关状态，初始化为1（关闭状态）

uint8_t already_countdown = 0;
uint8_t last_snooze_time = 0; // 记录上一次设置的贪睡时间

// 闹钟自动关闭相关变量
uint8_t alarm_auto_close_time = 0; // 闹钟自动关闭计时器（分钟）
uint8_t alarm_auto_close_flag = 0; // 闹钟自动关闭标志

switch_t time_set_key;

// 波动开关右边为1，左边为0

// time_set alarm snooze按键初始化
void switch_hw_init()
{
    SET_REG(PORTIDX, 4);
    SET_REG_BITS(PINMOD10, PINMOD1, PIN_MODE_OD_IPU);
    IO_TIME_SET_KEY = 1;

    SET_REG(PORTIDX, 5);
    SET_REG_BITS(PINMOD54, PINMOD4, PIN_MODE_OD_IPU);
    IO_ALARM_KEY = 1;
}

void switch_state_init()
{
    time_set_key.value = 0;
    time_set_key.lock = 0;
    time_set_key.state = 0;
    time_set_key.debounce = 0;
    time_set_key.longpress = 0;

    // 初始化上一次开关状态为当前状态，避免开机时误触发
    last_alarm_key_state = IO_ALARM_KEY;

    // 初始化贪睡时间记录为默认值
    if (last_snooze_time == 0)
    {
        last_snooze_time = 5; // 默认5分钟
    }

    // 初始化闹钟自动关闭计时器
    alarm_auto_close_time = 0;
    alarm_auto_close_flag = 0;
}

void scan_switch()
{
    uint8_t current_alarm_key_state = IO_ALARM_KEY;

    // 检测开关状态变化，如果闹钟正在响铃，任何开关波动都关闭闹钟
    if (last_alarm_key_state != current_alarm_key_state)
    {
        if (alarm_state) // 如果闹钟正在响铃
        {
            no_action_time = 0; // 重置无操作时间
            enter_sleep_time = 0;
            alarm_wakeup_flag = 0; // 清除闹钟唤醒标志

            alarm_state = 0;           // 关闭闹钟
            snooze_state = 0;          // 清除贪睡状态
            alarm_auto_close_time = 0; // 清除自动关闭计时器
            alarm_auto_close_flag = 0; // 清除自动关闭标志
            alarm_triggered_today = 0; // 彻底关闭今天的闹钟

            // 如果当前在贪睡倒计时显示状态，切换回实时时钟显示
            if (time_state == STATE_SNOOZE_CNT)
            {
                time_state = STATE_REAL_TIME;
            }

            // 发送关闭闹钟信号
            key_data[0] = 0x01;
            key_data[1] = 0x02;
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;
        }
    }

    if (current_alarm_key_state == 0)
    {
        if (last_alarm_key_state == 1 && current_alarm_key_state == 0)
        {
            no_action_time = 0; // 重置无操作时间
            enter_sleep_time = 0;

            snooze_state = 0;
            alarm_state = 0;
            alarm_auto_close_time = 0; // 清除自动关闭计时器
            alarm_auto_close_flag = 0; // 清除自动关闭标志

            alarm_triggered_today = 0;

            // 如果当前在贪睡倒计时显示状态，切换回实时时钟显示
            if (time_state == STATE_SNOOZE_CNT)
            {
                time_state = STATE_REAL_TIME;
            }
        }
    }

    if (current_alarm_key_state == 1)
    {
        alarm_state = 0;
        alarm_set_flag = 0;
        snooze_state = 0;
        alarm_auto_close_time = 0; // 清除自动关闭计时器
        alarm_auto_close_flag = 0; // 清除自动关闭标志

        // 如果当前在贪睡倒计时显示状态，切换回实时时钟显示
        if (time_state == STATE_SNOOZE_CNT)
        {
            time_state = STATE_REAL_TIME;
        }

        // 只在开关从关闭变为开启的瞬间设置闹钟为空状态（边沿触发）
        if (last_alarm_key_state == 0 && current_alarm_key_state == 1)
        {
            rtc.alarm_hour = ALARM_NOT_SET_HOUR;
            rtc.alarm_minute = ALARM_NOT_SET_MINUTE;
        }
    }

    // 更新上一次开关状态
    last_alarm_key_state = current_alarm_key_state;

    if (time_state != STATE_SET_ALARM_HOUR && time_state != STATE_SET_ALARM_MINUTE)
    {
        if (IO_ALARM_KEY)
        {
            alarm_flag = 0;
            led_other_flag &= ~0x02;
        }
        else
        {
            alarm_flag = 1;
            // 只有当闹钟开关打开且设置了有效的闹钟时间时，才显示闹钟图标
            if (rtc.alarm_hour != ALARM_NOT_SET_HOUR || rtc.alarm_minute != ALARM_NOT_SET_MINUTE)
            {
                led_other_flag |= 0x02;
            }
            else
            {
                led_other_flag &= ~0x02;
            }
        }
    }
}

void scan_timeset()
{
    switch_t *time_set;
    time_set = &time_set_key;
    if (!time_set->lock)
    {
        if (IO_TIME_SET_KEY == 0)
        {
            if (time_set->state == 1)
            {
                return;
            }
            time_set->debounce++;
            if (time_set->debounce >= SWITCH_DEBOUNCE_TIME)
            {
                time_set->debounce = 0;
                time_set->lock = SWITCH_LOCKED;
                time_set->value = SWITCH_TRGO_PRESS;
                no_action_time = 0; // 重置无操作时间
                enter_sleep_time = 0;
            }
        }
        else
        {
            time_set->debounce = 0;
            time_set->longpress = 0;
            time_set->state = 0;
        }
    }
    else
    {
        if (IO_TIME_SET_KEY == 0)
        {
            time_set->longpress++;
            if (time_set->longpress > SWITCH_LONGPRESS_TIME)
            {
                time_set->longpress = 0;
                time_set->lock = SWITCH_UNLOCKED;
                time_set->value = SWITCH_TRGO_LONG_PRESS;
                time_set->state = 1;
            }
        }
        else
        {
            time_set->debounce++;
            if (time_set->debounce >= SWITCH_DEBOUNCE_TIME)
            {
                time_set->debounce = 0;
                time_set->lock = SWITCH_UNLOCKED;
                time_set->value = SWITCH_TRGO_RELEASE;
            }
        }
    }
}

void time_set_exe()
{
    if (time_set_key.value == SWITCH_TRGO_RELEASE)
    {
        time_state = STATE_SET_TIME_HOUR;
        time_set_key.value = 0;
    }
    else if (time_set_key.value == SWITCH_TRGO_LONG_PRESS)
    {
        led_12_24_mode = !led_12_24_mode;
        time_set_key.value = 0;
    }
}

void switch_task()
{
    if (time_state == STATE_POWER_OFF)
        return;

    if (scan_switch_time > 10) // 10ms
    {
        scan_switch_time = 0;
        scan_switch();
        scan_timeset();
        time_set_exe();
    }
}
