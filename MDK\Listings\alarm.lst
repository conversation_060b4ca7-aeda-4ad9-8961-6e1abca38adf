C51 COMPILER V9.60.7.0   ALARM                                                             07/26/2025 11:36:03 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE ALARM
OBJECT MODULE PLACED IN .\Objects\alarm.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\alarm.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hardwa
                    -re) DEBUG OBJECTEXTEND PRINT(.\Listings\alarm.lst) TABS(2) OBJECT(.\Objects\alarm.obj)

line level    source

   1          #include "main.h"
   2          
   3          uint8_t scan_alarm_time = 0;
   4          uint8_t alarm_flag = 0;            // 闹钟标志
   5          uint8_t alarm_triggered_today = 0; // 当天闹钟是否已响过
   6          
   7          // 处理闹钟响起时的睡眠唤醒逻辑
   8          void handle_alarm_wakeup()
   9          {
  10   1          // 记录是否从睡眠状态唤醒
  11   1          uint8_t wakeup_from_sleep = (time_state == STATE_SLEEP);
  12   1      
  13   1          // 如果当前处于睡眠状态，闹钟响起时需要唤醒
  14   1          if (wakeup_from_sleep)
  15   1          {
  16   2              // 恢复到睡眠前的状态，而不是强制回到实时时钟
  17   2              time_state = time_state_before_sleep;
  18   2              pwm_led_on_flag = 1;
  19   2              pwm_led_off_flag = 0;
  20   2      
  21   2              // 启动显示LED渐亮呼吸效果
  22   2              display_led_breath_on_flag = 1;
  23   2              display_led_breath_off_flag = 0;
  24   2              breath_display_on_cnt = 0;
  25   2              breath_display_on_duty = 0; // 从0开始渐亮
  26   2          }
  27   1      
  28   1          // 根据进入睡眠前的氛围灯状态决定闹钟响起时的行为
  29   1          if (ambient_light_before_sleep == KEY_LED_LEVEL_0)
  30   1          {
  31   2              // 进入睡眠前氛围灯是灭的，设置标志等待触摸唤醒
  32   2              alarm_wakeup_flag = 1;
  33   2              // 如果是从睡眠状态唤醒，设为KEY_LED_LEVEL_1
  34   2              if (wakeup_from_sleep)
  35   2              {
  36   3                  ambient_light = KEY_LED_LEVEL_1;
  37   3                  key_led_light = KEY_LED_LEVEL_1; // 同步按键LED亮度
  38   3                  white_duty_config = 25;          // 设置为KEY_LED_LEVEL_1对应的亮度
  39   3              }
  40   2          }
  41   1          else
  42   1          {
  43   2              // 进入睡眠前氛围灯是亮的，直接恢复原亮度等级进行呼吸
  44   2              ambient_light = ambient_light_before_sleep;
  45   2              key_led_light = ambient_light_before_sleep; // 同步按键LED亮度
  46   2              set_white_duty_config_by_ambient_light();
  47   2              alarm_wakeup_flag = 0; // 不需要等待触摸唤醒
  48   2          }
  49   1      }
  50          
  51          void alarm_wakeup()
  52          {
  53   1          uint16_t i = 0;
  54   1          uint16_t s = 0;
C51 COMPILER V9.60.7.0   ALARM                                                             07/26/2025 11:36:03 PAGE 2   

  55   1          uint8_t last_check_hour = 0;
  56   1          i = rtc.hour * 60 + rtc.minute;
  57   1          s = rtc.alarm_hour * 60 + rtc.alarm_minute;
  58   1      
  59   1          // 检测跨天，当小时数从23变为0时，说明是新的一天
  60   1          if (rtc.hour == 0 && last_check_hour == 23)
  61   1          {
  62   2              alarm_triggered_today = 0;
  63   2          }
  64   1      
  65   1          last_check_hour = rtc.hour;
  66   1      
  67   1          if (alarm_flag && !alarm_set_flag)
  68   1          {
  69   2              if (alarm_state || time_state == STATE_SET_SNOOZE_CNT)
  70   2                  return;
  71   2      
  72   2              // 检查闹钟是否已设置有效时间
  73   2              if (rtc.alarm_hour == ALARM_NOT_SET_HOUR && rtc.alarm_minute == ALARM_NOT_SET_MINUTE)
  74   2                  return;
  75   2      
  76   2              if (snooze_state == 0)
  77   2              {
  78   3                  if (i == s && !alarm_triggered_today)
  79   3                  {
  80   4                      alarm_state = 1;
  81   4                      alarm_triggered_today = 1;
  82   4                      alarm_auto_close_time = 3; // 设置3分钟自动关闭
  83   4      
  84   4                      // 处理闹钟响起时的睡眠唤醒逻辑
  85   4                      handle_alarm_wakeup();
  86   4      
  87   4                      // 发送闹钟响铃信号
  88   4                      key_data[0] = 0x01;
  89   4                      key_data[1] = 0x01;
  90   4                      uart0_send(key_data, 4);
  91   4                      key_data[0] = 0;
  92   4                      key_data[1] = 0;
  93   4                  }
  94   3              }
  95   2              else
  96   2              {
  97   3                  if (snooze_time_ended)
  98   3                  {
  99   4                      snooze_time_ended = 0;
 100   4      
 101   4                      alarm_state = 1;
 102   4                      alarm_auto_close_time = 3; // 贪睡闹钟也设置3分钟自动关闭
 103   4      
 104   4                      // 处理贪睡闹钟响起时的睡眠唤醒逻辑
 105   4                      handle_alarm_wakeup();
 106   4      
 107   4                      // 发送闹钟响铃信号
 108   4                      key_data[0] = 0x01;
 109   4                      key_data[1] = 0x01;
 110   4                      uart0_send(key_data, 4);
 111   4                      key_data[0] = 0;
 112   4                      key_data[1] = 0;
 113   4                  }
 114   3              }
 115   2          }
 116   1      }
C51 COMPILER V9.60.7.0   ALARM                                                             07/26/2025 11:36:03 PAGE 3   

 117          
 118          // 处理倒计时结束后的标志位并进入实时时钟模式
 119          void countdown_exe()
 120          {
 121   1          if (countdown_time_ended)
 122   1          {
 123   2              countdown_time_ended = 0;
 124   2              count_down_flag = 0;
 125   2      
 126   2              time_state = STATE_REAL_TIME;
 127   2              music_mode = MODE_IDE;
 128   2      
 129   2              ble_pair_flag = 0;      // 关闭蓝牙配对标志位
 130   2              ble_reconnect_flag = 0; // 关闭蓝牙重连标志位
 131   2              led_other_flag = 0;     // 关闭其他标志位
 132   2      
 133   2              // 发送挂起信号
 134   2              key_data[0] = 0x01;
 135   2              key_data[1] = 0x10;
 136   2              uart0_send(key_data, 4);
 137   2              key_data[0] = 0;
 138   2              key_data[1] = 0;
 139   2          }
 140   1      }
 141          
 142          // 处理闹钟自动关闭
 143          void alarm_auto_close_exe()
 144          {
 145   1          if (alarm_auto_close_flag)
 146   1          {
 147   2              alarm_auto_close_flag = 0;
 148   2      
 149   2              // 3分钟时间到，自动关闭闹钟，不进入贪睡
 150   2              alarm_state = 0;
 151   2              snooze_state = 0;
 152   2              alarm_auto_close_time = 0;
 153   2              alarm_triggered_today = 1; // 标记今天的闹钟已响完
 154   2      
 155   2              // 清除闹钟唤醒标志，恢复正常的氛围灯控制
 156   2              alarm_wakeup_flag = 0;
 157   2      
 158   2              // 如果当前在贪睡倒计时显示状态，切换回实时时钟显示
 159   2              if (time_state == STATE_SNOOZE_CNT)
 160   2              {
 161   3                  time_state = STATE_REAL_TIME;
 162   3              }
 163   2      
 164   2              // 重置睡眠计时器，闹钟结束后按正常逻辑40s无操作进入睡眠
 165   2              no_action_time = 0;
 166   2              enter_sleep_time = 0;
 167   2      
 168   2              // 发送关闭闹钟信号
 169   2              key_data[0] = 0x01;
 170   2              key_data[1] = 0x02;
 171   2              uart0_send(key_data, 4);
 172   2              key_data[0] = 0;
 173   2              key_data[1] = 0;
 174   2          }
 175   1      }
 176          
 177          void alarm_task(void)
 178          {
C51 COMPILER V9.60.7.0   ALARM                                                             07/26/2025 11:36:03 PAGE 4   

 179   1          if (time_state == STATE_POWER_OFF)
 180   1              return;
 181   1      
 182   1          alarm_wakeup();
 183   1          countdown_exe();
 184   1          alarm_auto_close_exe();
 185   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    449    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =      3       2
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
