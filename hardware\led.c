#include "main.h"

/**
 *  ————a————
 * |         |
 * f         b
 * |         |
 *  ————g————
 * |         |
 * e         c
 * |         |
 *  ————d————
 */

// gfedcba
uint8_t led_map[10] =
    {0x3f,  // 0 0b00111111
     0x06,  // 1 0b00000110
     0x5b,  // 2 0b01011011
     0x4f,  // 3 0b01001111
     0x66,  // 4 0b01100110
     0x6d,  // 5 0b01101101
     0x7d,  // 6 0b01111101
     0x07,  // 7 0b00000111
     0x7f,  // 8 0b01111111
     0x6f}; // 9 0b01101111

// 横线（--）的段码：只有g段亮
#define DASH_PATTERN 0x40 // 0b01000000

uint8_t led_offset[4] = {
    LED_4G, // 千位起始偏移
    LED_3G, // 百位起始偏移
    LED_2G, // 十位起始偏移
    LED_1G  // 个位起始偏移
};
uint8_t digit_num = 0;

uint8_t led_show_state = 0;
uint8_t timer_blink_count = 0;

uint8_t led_12_24_mode = 0; // 0:24小时,1:12小时

uint8_t led_hour_index = 0;
uint8_t led_hour_num = 0;
uint8_t led_minute_index = 0;
uint8_t led_minute_num = 0;
uint8_t led_other_flag = 0;
uint8_t led_other_num = 0;

uint16_t led_set_alarm_blink_hour = 0;
uint16_t led_set_alarm_blink_minute = 0;

uint16_t led_set_time_blink_hour = 0;
uint16_t led_set_time_blink_minute = 0;

// 显示设置倒计时
uint16_t led_set_countdown_blink = 0;

// 显示设置snooze
uint16_t led_set_snooze_blink = 0;

uint16_t led_blink_num_set = 0;
uint16_t led_blink_num_set_max = 0;

uint8_t bt_name_num_thousand = 0;
uint8_t bt_name_num_hundred = 0;
uint8_t bt_name_num_ten = 0;
uint8_t bt_name_num_one = 0;

// 闪烁控制变量
uint16_t led_set_bt_blink_thousand = 0;
uint16_t led_set_bt_blink_hundred = 0;
uint16_t led_set_bt_blink_ten = 0;
uint16_t led_set_bt_blink_one = 0;

static uint8_t last_time_state = 0xFF; // 记录上一次的状态

void led_init()
{
    SET_REG(PORTIDX, 0);
    SET_REG_BITS(PINMOD10, PINMOD0, PIN_MODE_PP); // KEY_LED_TIMER
    SET_REG_BITS(PINMOD10, PINMOD1, PIN_MODE_PP); // KEY_LED_MUSIC
    KEY_LED_TIMER = 1;
    KEY_LED_MUSIC = 1;

    SET_REG(PORTIDX, 1);
    SET_REG_BITS(PINMOD10, PINMOD1, PIN_MODE_PP); // LED_WHITE
    SET_REG_BITS(PINMOD54, PINMOD4, PIN_MODE_PP); // c1
    SET_REG_BITS(PINMOD54, PINMOD5, PIN_MODE_PP); // c2
    SET_REG_BITS(PINMOD76, PINMOD6, PIN_MODE_PP); // c3
    SET_REG_BITS(PINMOD76, PINMOD7, PIN_MODE_PP); // c4
    LED_WHITE = 1;
    P1_4 = 1;
    P1_5 = 1;
    P1_6 = 1;
    P1_7 = 1;

    SET_REG(PORTIDX, 2);
    SET_REG_BITS(PINMOD10, PINMOD0, PIN_MODE_PP); // s7
    SET_REG_BITS(PINMOD10, PINMOD1, PIN_MODE_PP); // s8
    SET_REG_BITS(PINMOD32, PINMOD2, PIN_MODE_PP); // s9
    SET_REG_BITS(PINMOD54, PINMOD4, PIN_MODE_PP); // s1
    SET_REG_BITS(PINMOD54, PINMOD5, PIN_MODE_PP); // s2
    SET_REG_BITS(PINMOD76, PINMOD6, PIN_MODE_PP); // s3
    SET_REG_BITS(PINMOD76, PINMOD7, PIN_MODE_PP); // s4
    P2_0 = 1;
    P2_1 = 1;
    P2_2 = 1;
    P2_4 = 1;
    P2_5 = 1;
    P2_6 = 1;
    P2_7 = 1;

    SET_REG(PORTIDX, 3);
    SET_REG_BITS(PINMOD32, PINMOD2, PIN_MODE_PP); // KEY_LED_AUX
    SET_REG_BITS(PINMOD32, PINMOD3, PIN_MODE_PP); // KEY_LED_BT
    SET_REG_BITS(PINMOD76, PINMOD6, PIN_MODE_PP); // s5
    SET_REG_BITS(PINMOD76, PINMOD7, PIN_MODE_PP); // s6
    KEY_LED_AUX = 1;
    KEY_LED_BT = 1;
    P3_6 = 1;
    P3_7 = 1;

    // key_led
    SET_REG(PORTIDX, 4);
    SET_REG_BITS(PINMOD32, PINMOD2, PIN_MODE_PP); // KEY_LED_POWER
    SET_REG_BITS(PINMOD32, PINMOD3, PIN_MODE_PP); // KEY_LED_VOLUME_DOWN
    KEY_LED_POWER = 1;
    KEY_LED_VOLUME_DOWN = 1;

    SET_REG(PORTIDX, 5);
    SET_REG_BITS(PINMOD10, PINMOD0, PIN_MODE_PP); // KEY_LED_PP
    SET_REG_BITS(PINMOD10, PINMOD1, PIN_MODE_PP); // KEY_LED_VOLUME_UP
    SET_REG_BITS(PINMOD54, PINMOD5, PIN_MODE_PP); // LED_SNOOZE
    SET_REG_BITS(PINMOD76, PINMOD6, PIN_MODE_PP); // KEY_LED_AMB

    KEY_LED_PP = 1;
    KEY_LED_VOLUME_UP = 1;
    LED_SNOOZE = 1;
    KEY_LED_AMB = 1;
}

void all_led_seg_off()
{
    C1 = 1;
    C2 = 1;
    C3 = 1;
    C4 = 1;
    S1 = 1;
    S2 = 1;
    S3 = 1;
    S4 = 1;
    S5 = 1;
    S6 = 1;
    S7 = 1;
    S8 = 1;
    S9 = 1;
}

void led_display(uint8_t num, uint8_t state) // state 0 开启 1 关闭
{
    all_led_seg_off();
    switch (num)
    {
    case LED_4G:
        C4 = state;
        S1 = state;
        break;
    case LED_4F:
        C3 = state;
        S1 = state;
        break;
    case LED_4E:
        C2 = state;
        S1 = state;
        break;
    case LED_4D:
        C1 = state;
        S1 = state;
        break;
    case LED_4C:
        C4 = state;
        S2 = state;
        break;
    case LED_4B:
        C3 = state;
        S2 = state;
        break;
    case LED_4A:
        C2 = state;
        S2 = state;
        break;
    case LED_3G:
        C1 = state;
        S2 = state;
        break;
    case LED_3F:
        C4 = state;
        S3 = state;
        break;
    case LED_3E:
        C3 = state;
        S3 = state;
        break;
    case LED_3D:
        C2 = state;
        S3 = state;
        break;
    case LED_3C:
        C1 = state;
        S3 = state;
        break;
    case LED_3B:
        C4 = state;
        S4 = state;
        break;
    case LED_3A:
        C3 = state;
        S4 = state;
        break;
    case LED_2G:
        C2 = state;
        S4 = state;
        break;
    case LED_2F:
        C1 = state;
        S4 = state;
        break;
    case LED_2E:
        C4 = state;
        S5 = state;
        break;
    case LED_2D:
        C3 = state;
        S5 = state;
        break;
    case LED_2C:
        C2 = state;
        S5 = state;
        break;
    case LED_2B:
        C1 = state;
        S5 = state;
        break;
    case LED_2A:
        C4 = state;
        S6 = state;
        break;
    case LED_1G:
        C3 = state;
        S6 = state;
        break;
    case LED_1F:
        C2 = state;
        S6 = state;
        break;
    case LED_1E:
        C1 = state;
        S6 = state;
        break;
    case LED_1D:
        C4 = state;
        S7 = state;
        break;
    case LED_1C:
        C3 = state;
        S7 = state;
        break;
    case LED_1B:
        C2 = state;
        S7 = state;
        break;
    case LED_1A:
        C1 = state;
        S7 = state;
        break;
    case LED_PM:
        C3 = state;
        S8 = state;
        break;
    case LED_DOT:
        C3 = state;
        S9 = state;
        break;
    case LED_ALARM:
        C2 = state;
        S9 = state;
        break;
    }
}

uint8_t show_digit(uint8_t digit, uint8_t position)
{
    if (digit_num < 7)
    {
        if (led_map[digit] & (0x40 >> digit_num))
        {
            led_display(led_offset[position] + digit_num, 0);
        }
        else
        {
            all_led_seg_off();
        }
        digit_num++;
    }
    else
    {
        all_led_seg_off();
        digit_num = 0;
        return 9;
    }
    return 0;
}

// 显示横线（--）的函数
uint8_t show_dash(uint8_t position, uint8_t state)
{
    if (digit_num < 7)
    {
        if (DASH_PATTERN & (0x40 >> digit_num))
        {
            led_display(led_offset[position] + digit_num, state);
        }
        else
        {
            all_led_seg_off();
        }
        digit_num++;
    }
    else
    {
        all_led_seg_off();
        digit_num = 0;
        return 9;
    }
    return 0;
}

// 显示闹钟小时位--的函数（仿照show_time_hour的模式）
unsigned char show_alarm_dash_hour(uint8_t state)
{
    // 固定扫描频率
    if (led_hour_index == 0)
    {
        // 显示小时十位的横线
        if (led_hour_num < 7)
        {
            if (DASH_PATTERN & (0x40 >> led_hour_num))
            {
                led_display(LED_4G + led_hour_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_hour_num++;
        }
        else
        {
            all_led_seg_off();
            led_hour_num = 0;
            led_hour_index = 1;
        }
    }
    else
    {
        // 显示小时个位的横线
        if (led_hour_num < 7)
        {
            if (DASH_PATTERN & (0x40 >> led_hour_num))
            {
                led_display(LED_3G + led_hour_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_hour_num++;
        }
        else
        {
            all_led_seg_off();
            led_hour_num = 0;
            led_hour_index = 0;
            return 9;
        }
    }
    return 0;
}

// 显示闹钟分钟位--的函数（仿照show_time_minute的模式）
unsigned char show_alarm_dash_minute(uint8_t state)
{
    // 固定扫描频率
    if (led_minute_index == 0)
    {
        // 显示分钟十位的横线
        if (led_minute_num < 7)
        {
            if (DASH_PATTERN & (0x40 >> led_minute_num))
            {
                led_display(LED_2G + led_minute_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 1;
        }
    }
    else
    {
        // 显示分钟个位的横线
        if (led_minute_num < 7)
        {
            if (DASH_PATTERN & (0x40 >> led_minute_num))
            {
                led_display(LED_1G + led_minute_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 0;
            return 9;
        }
    }
    return 0;
}

unsigned char show_time_hour(unsigned char hour, uint8_t state)
{
    uint8_t num_ones = hour % 10;
    uint8_t num_tens = hour / 10;

    // 固定扫描频率
    if (led_hour_index == 0)
    {
        // 总是显示当前段，由led_map决定是否点亮
        if (led_hour_num < 7)
        {
            if (led_map[num_tens] & (0x40 >> led_hour_num))
            {
                led_display(LED_4G + led_hour_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_hour_num++;
        }
        else
        {
            all_led_seg_off();
            led_hour_num = 0;
            led_hour_index = 1;
        }
    }
    else
    {
        if (led_hour_num < 7)
        {
            if (led_map[num_ones] & (0x40 >> led_hour_num))
            {
                led_display(LED_3G + led_hour_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_hour_num++;
        }
        else
        {
            all_led_seg_off();
            led_hour_num = 0;
            led_hour_index = 0;
            return 9;
        }
    }
    return 0;
}

unsigned char show_time_minute(unsigned char minute, uint8_t state)
{
    uint8_t num_ones = minute % 10;
    uint8_t num_tens = minute / 10;

    // 固定扫描频率
    if (led_minute_index == 0)
    {
        // 总是显示当前段，由led_map决定是否点亮
        if (led_minute_num < 7)
        {
            if (led_map[num_tens] & (0x40 >> led_minute_num))
            {
                led_display(LED_2G + led_minute_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 1;
        }
    }
    else
    {
        if (led_minute_num < 7)
        {
            if (led_map[num_ones] & (0x40 >> led_minute_num))
            {
                led_display(LED_1G + led_minute_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 0;
            return 9;
        }
    }
    return 0;
}

// 特殊显示函数：小时十位显示倒F，个位不亮，分钟正常显示
unsigned char show_special_time_with_inverted_f(unsigned char minute, uint8_t state)
{
    uint8_t num_ones = minute % 10;
    uint8_t num_tens = minute / 10;
    uint8_t inverted_f_pattern = 0x78; // 倒F的段码：gfed段亮 (0b01111000)

    // 固定扫描频率
    if (led_minute_index == 0)
    {
        // 显示小时十位的倒F
        if (led_minute_num < 7)
        {
            if (inverted_f_pattern & (0x40 >> led_minute_num))
            {
                led_display(LED_4G + led_minute_num, state); // 小时十位位置
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 1;
        }
    }
    else if (led_minute_index == 1)
    {
        // 小时个位不亮，直接跳过
        all_led_seg_off();
        led_minute_num = 0;
        led_minute_index = 2;
    }
    else if (led_minute_index == 2)
    {
        // 显示分钟十位
        if (led_minute_num < 7)
        {
            if (led_map[num_tens] & (0x40 >> led_minute_num))
            {
                led_display(LED_2G + led_minute_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 3;
        }
    }
    else
    {
        // 显示分钟个位
        if (led_minute_num < 7)
        {
            if (led_map[num_ones] & (0x40 >> led_minute_num))
            {
                led_display(LED_1G + led_minute_num, state);
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 0;
            return 9;
        }
    }
    return 0;
}

// 设置时的特殊显示函数：倒F和数字都闪烁
unsigned char show_special_time_with_inverted_f_blink(unsigned char minute, uint8_t show_flag)
{
    uint8_t num_ones = minute % 10;
    uint8_t num_tens = minute / 10;
    uint8_t inverted_f_pattern = 0x78; // 倒F的段码：gfed段亮 (0b01111000)

    // 固定扫描频率
    if (led_minute_index == 0)
    {
        // 显示小时十位的倒F（根据show_flag决定是否显示）
        if (led_minute_num < 7)
        {
            if (show_flag && (inverted_f_pattern & (0x40 >> led_minute_num)))
            {
                led_display(LED_4G + led_minute_num, 0); // 小时十位位置
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 1;
        }
    }
    else if (led_minute_index == 1)
    {
        // 小时个位不亮，直接跳过
        all_led_seg_off();
        led_minute_num = 0;
        led_minute_index = 2;
    }
    else if (led_minute_index == 2)
    {
        // 显示分钟十位（根据show_flag决定是否显示）
        if (led_minute_num < 7)
        {
            if (show_flag && (led_map[num_tens] & (0x40 >> led_minute_num)))
            {
                led_display(LED_2G + led_minute_num, 0);
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 3;
        }
    }
    else
    {
        // 显示分钟个位（根据show_flag决定是否显示）
        if (led_minute_num < 7)
        {
            if (show_flag && (led_map[num_ones] & (0x40 >> led_minute_num)))
            {
                led_display(LED_1G + led_minute_num, 0);
            }
            else
            {
                all_led_seg_off();
            }
            led_minute_num++;
        }
        else
        {
            all_led_seg_off();
            led_minute_num = 0;
            led_minute_index = 0;
            return 9;
        }
    }
    return 0;
}

unsigned char led_show_other(uint8_t state)
{
    if (led_other_flag & (0x80 >> led_other_num))
    {
        switch (led_other_num)
        {
        case 1:
            led_display(LED_PM, state);
            break;
        case 5:
            led_display(LED_DOT, state);
            break;
        case 6:
            led_display(LED_ALARM, state);
            break;
        }
    }
    else
    {
        all_led_seg_off();
    }

    led_other_num++;
    if (led_other_num == 7)
    {
        led_other_num = 0;
        return 9;
    }
}

void show_real_time()
{
    uint8_t hour = rtc.hour;
    uint8_t minute = rtc.minute;

    if (hour >= 12 && led_12_24_mode)
    {
        led_other_flag |= 0x40; // Set bit 6 for PM
    }
    else
    {
        led_other_flag &= ~0x40; // Clear bit 6 for PM
    }

    led_other_flag |= 0x04; // 显示点

    if (led_12_24_mode == 1)
    {
        if (hour == 0)
        {
            hour = 12;
        }
        else if (hour > 12)
        {
            hour = hour - 12;
        }
    }

    switch (led_show_state)
    {
    case 0:
        if (show_time_hour(hour, 0) == 9)
        {
            led_show_state = 1;
        }
        break;
    case 1:
        if (show_time_minute(minute, 0) == 9)
        {
            led_show_state = 2;
        }
        break;
    case 2:
        if (led_show_other(0) == 9)
        {
            led_show_state = 0;
        }
        break;
    }
}

void show_timer_countdown()
{
    switch (led_show_state)
    {
    case 0:
        led_show_state = 1;
        break;
    case 1:
        if (show_special_time_with_inverted_f(rtc.countdown_time, 0) == 9)
        {
            led_show_state = 2;
        }
        break;
    case 2:
        if (led_show_other(0) == 9)
        {
            led_show_state = 0;
        }
        break;
    }
}

void show_snooze_cnt()
{
    switch (led_show_state)
    {
    case 0:
        led_show_state = 1;
        break;
    case 1:
        if (show_special_time_with_inverted_f(rtc.snooze_time, 0) == 9)
        {
            led_show_state = 2;
        }
        break;
    case 2:
        if (led_show_other(0) == 9)
        {
            led_show_state = 0;
        }
        break;
    }
}

void set_real_time()
{
    uint8_t hour = rtc.hour;
    uint8_t minute = rtc.minute;

    if (hour >= 12 && led_12_24_mode)
    {
        led_other_flag |= 0x40; // Set bit 6 for PM
    }
    else
    {
        led_other_flag &= ~0x40; // Clear bit 6 for PM
    }

    led_other_flag |= 0x04; // 显示点

    if (led_12_24_mode == 1)
    {
        if (hour == 0)
        {
            hour = 12;
        }
        else if (hour > 12)
        {
            hour = hour - 12;
        }
    }

    if (time_state == STATE_SET_TIME_HOUR)
    {
        led_set_time_blink_minute = 0;
        led_set_time_blink_hour++;
        if (led_set_time_blink_hour >= led_blink_num_set_max)
            led_set_time_blink_hour = 0;
    }

    if (time_state == STATE_SET_TIME_MINUTE)
    {
        led_set_time_blink_hour = 0;
        led_set_time_blink_minute++;
        if (led_set_time_blink_minute >= led_blink_num_set_max)
            led_set_time_blink_minute = 0;
    }

    switch (led_show_state)
    {
    case 0:
        if (led_set_time_blink_hour <= led_blink_num_set)
        {
            if (show_time_hour(hour, 0) == 9)
            {
                led_show_state = 1;
            }
        }
        else
        {
            if (show_time_hour(hour, 1) == 9)
            {
                led_show_state = 1;
            }
        }
        break;
    case 1:
        if (led_set_time_blink_minute <= led_blink_num_set)
        {
            if (show_time_minute(minute, 0) == 9)
            {
                led_show_state = 2;
            }
        }
        else
        {
            if (show_time_minute(minute, 1) == 9)
            {
                led_show_state = 2;
            }
        }
        break;
    case 2:
        if (led_show_other(0) == 9)
        {
            led_show_state = 0;
        }
        break;
    }
}

void set_alarm_time()
{
    uint8_t hour = rtc.alarm_hour;
    uint8_t minute = rtc.alarm_minute;
    uint8_t is_alarm_not_set = (hour == ALARM_NOT_SET_HOUR && minute == ALARM_NOT_SET_MINUTE);

    if (!is_alarm_not_set && hour >= 12 && led_12_24_mode)
    {
        led_other_flag |= 0x40; // Set bit 6 for PM
    }
    else
    {
        led_other_flag &= ~0x40; // Clear bit 6 for PM
    }

    led_other_flag |= 0x04; // 显示点

    if (!is_alarm_not_set && led_12_24_mode == 1)
    {
        if (hour == 0)
        {
            hour = 12;
        }
        else if (hour > 12)
        {
            hour = hour - 12;
        }
    }

    if (time_state == STATE_SET_ALARM_HOUR)
    {
        led_set_alarm_blink_minute = 0;
        led_set_alarm_blink_hour++;
        if (led_set_alarm_blink_hour >= led_blink_num_set_max)
            led_set_alarm_blink_hour = 0;
        if (led_set_alarm_blink_hour <= led_blink_num_set)
        {
            led_other_flag |= 0x02; // 显示闹钟标志
        }
        else
        {
            led_other_flag &= ~0x02; // 隐藏闹钟标志
        }
    }

    if (time_state == STATE_SET_ALARM_MINUTE)
    {
        led_set_alarm_blink_hour = 0;
        led_set_alarm_blink_minute++;
        if (led_set_alarm_blink_minute >= led_blink_num_set_max)
            led_set_alarm_blink_minute = 0;
        if (led_set_alarm_blink_minute <= led_blink_num_set)
        {
            led_other_flag |= 0x02; // 显示闹钟标志
        }
        else
        {
            led_other_flag &= ~0x02; // 隐藏闹钟标志
        }
    }

    switch (led_show_state)
    {
    case 0:
        if (is_alarm_not_set)
        {
            // 显示小时位的--，根据设置状态决定是否闪烁
            if (time_state == STATE_SET_ALARM_HOUR)
            {
                // 设置小时时，小时位闪烁
                if (led_set_alarm_blink_hour <= led_blink_num_set)
                {
                    if (show_alarm_dash_hour(0) == 9) // 显示小时位的--
                    {
                        led_show_state = 1;
                    }
                }
                else
                {
                    if (show_alarm_dash_hour(1) == 9) // 闪烁时关闭显示
                    {
                        led_show_state = 1;
                    }
                }
            }
            else
            {
                // 非设置小时状态，小时位正常显示
                if (show_alarm_dash_hour(0) == 9) // 显示小时位的--
                {
                    led_show_state = 1;
                }
            }
        }
        else
        {
            if (led_set_alarm_blink_hour <= led_blink_num_set)
            {
                if (show_time_hour(hour, 0) == 9)
                {
                    led_show_state = 1;
                }
            }
            else
            {
                if (show_time_hour(hour, 1) == 9)
                {
                    led_show_state = 1;
                }
            }
        }
        break;
    case 1:
        if (is_alarm_not_set)
        {
            // 显示分钟位的--，根据设置状态决定是否闪烁
            if (time_state == STATE_SET_ALARM_MINUTE)
            {
                // 设置分钟时，分钟位闪烁
                if (led_set_alarm_blink_minute <= led_blink_num_set)
                {
                    if (show_alarm_dash_minute(0) == 9) // 显示分钟位的--
                    {
                        led_show_state = 2;
                    }
                }
                else
                {
                    if (show_alarm_dash_minute(1) == 9) // 闪烁时关闭显示
                    {
                        led_show_state = 2;
                    }
                }
            }
            else
            {
                // 非设置分钟状态，分钟位正常显示
                if (show_alarm_dash_minute(0) == 9) // 显示分钟位的--
                {
                    led_show_state = 2;
                }
            }
        }
        else
        {
            if (led_set_alarm_blink_minute <= led_blink_num_set)
            {
                if (show_time_minute(minute, 0) == 9)
                {
                    led_show_state = 2;
                }
            }
            else
            {
                if (show_time_minute(minute, 1) == 9)
                {
                    led_show_state = 2;
                }
            }
        }
        break;
    case 2:
        if (led_show_other(0) == 9)
        {
            led_show_state = 0;
        }
        break;
    }
}

void set_timer_countdown()
{
    uint8_t show_flag;
    led_set_countdown_blink++;
    if (led_set_countdown_blink >= led_blink_num_set_max)
        led_set_countdown_blink = 0;

    if (led_set_countdown_blink <= led_blink_num_set)
    {
        show_flag = 1;
    }
    else
    {
        show_flag = 0;
    }
    show_special_time_with_inverted_f_blink(rtc.countdown_time, show_flag);
}

void set_snooze_cnt()
{
    uint8_t show_flag;
    led_set_snooze_blink++;
    if (led_set_snooze_blink >= led_blink_num_set_max)
        led_set_snooze_blink = 0;
    if (led_set_snooze_blink <= led_blink_num_set)
    {
        show_flag = 1;
    }
    else
    {
        show_flag = 0;
    }
    show_special_time_with_inverted_f_blink(rtc.snooze_time, show_flag);
}

void set_bt_num()
{
    uint8_t digit1 = bt_name_num_thousand; // 千位
    uint8_t digit2 = bt_name_num_hundred;  // 百位
    uint8_t digit3 = bt_name_num_ten;      // 十位
    uint8_t digit4 = bt_name_num_one;      // 个位

    led_other_flag = 0; // 清除其他显示标志

    switch (time_state)
    {
    case STATE_SET_BT_NUM_THOUSAND:
        led_set_bt_blink_hundred = 0;
        led_set_bt_blink_ten = 0;
        led_set_bt_blink_one = 0;

        led_set_bt_blink_thousand++;
        if (led_set_bt_blink_thousand >= led_blink_num_set_max)
            led_set_bt_blink_thousand = 0;
        break;

    case STATE_SET_BT_NUM_HUNDRED:
        led_set_bt_blink_thousand = 0;
        led_set_bt_blink_ten = 0;
        led_set_bt_blink_one = 0;

        led_set_bt_blink_hundred++;
        if (led_set_bt_blink_hundred >= led_blink_num_set_max)
            led_set_bt_blink_hundred = 0;
        break;

    case STATE_SET_BT_NUM_TEN:
        led_set_bt_blink_thousand = 0;
        led_set_bt_blink_hundred = 0;
        led_set_bt_blink_one = 0;

        led_set_bt_blink_ten++;
        if (led_set_bt_blink_ten >= led_blink_num_set_max)
            led_set_bt_blink_ten = 0;
        break;

    case STATE_SET_BT_NUM_ONE:
        led_set_bt_blink_thousand = 0;
        led_set_bt_blink_hundred = 0;
        led_set_bt_blink_ten = 0;

        led_set_bt_blink_one++;
        if (led_set_bt_blink_one >= led_blink_num_set_max)
            led_set_bt_blink_one = 0;
        break;
    }

    // 显示逻辑 - 四个数字位
    switch (led_show_state)
    {
    case 0: // 千位
        if ((time_state != STATE_SET_BT_NUM_THOUSAND) ||
            (led_set_bt_blink_thousand <= led_blink_num_set))
        {
            // 假设我们用led_map数组显示数字，调用类似显示小时的函数
            // 使用类似的逻辑显示千位数字
            if (show_digit(digit1, 0) == 9) // 0表示第一个位置
            {
                led_show_state = 1;
            }
        }
        else
        {
            led_show_state = 1;
        }
        break;

    case 1: // 百位
        if ((time_state != STATE_SET_BT_NUM_HUNDRED) ||
            (led_set_bt_blink_hundred <= led_blink_num_set))
        {
            if (show_digit(digit2, 1) == 9) // 1表示第二个位置
            {
                led_show_state = 2;
            }
        }
        else
        {
            led_show_state = 2;
        }
        break;

    case 2: // 十位
        if ((time_state != STATE_SET_BT_NUM_TEN) ||
            (led_set_bt_blink_ten <= led_blink_num_set))
        {
            if (show_digit(digit3, 2) == 9) // 2表示第三个位置
            {
                led_show_state = 3;
            }
        }
        else
        {
            led_show_state = 3;
        }
        break;

    case 3: // 个位
        if ((time_state != STATE_SET_BT_NUM_ONE) ||
            (led_set_bt_blink_one <= led_blink_num_set))
        {
            if (show_digit(digit4, 3) == 9) // 3表示第四个位置
            {
                led_show_state = 4;
            }
        }
        else
        {
            led_show_state = 4;
        }
        break;

    case 4: // 其他显示(例如点或其他指示器)
        if (led_show_other(0) == 9)
        {
            led_show_state = 0;
        }
        break;
    }
}

void show_bt_num()
{
    // 将蓝牙名称拆分为四个位显示
    uint8_t digit1 = bt_name_num_thousand; // 千位
    uint8_t digit2 = bt_name_num_hundred;  // 百位
    uint8_t digit3 = bt_name_num_ten;      // 十位
    uint8_t digit4 = bt_name_num_one;      // 个位

    led_other_flag = 0; // 清除其他显示标志

    switch (led_show_state)
    {
    case 0: // 千位
        if (show_digit(digit1, 0) == 9)
        {
            led_show_state = 1;
        }
        break;

    case 1: // 百位
        if (show_digit(digit2, 1) == 9)
        {
            led_show_state = 2;
        }
        break;

    case 2: // 十位
        if (show_digit(digit3, 2) == 9)
        {
            led_show_state = 3;
        }
        break;

    case 3: // 个位
        if (show_digit(digit4, 3) == 9)
        {
            led_show_state = 4;
        }
        break;

    case 4: // 其他显示(点或其他指示器)
        if (led_show_other(0) == 9)
        {
            led_show_state = 0;
        }
        break;
    }
}

void led_task()
{
    if (last_time_state != time_state)
    {
        led_show_state = 0;
        digit_num = 0;
        led_hour_index = 0;
        led_hour_num = 0;
        led_minute_index = 0;
        led_minute_num = 0;
        led_other_num = 0;
        last_time_state = time_state;
    }

    if (key_led_light == KEY_LED_LEVEL_4)
    {
        led_blink_num_set_max = 4000;
        led_blink_num_set = 2000;
    }
    else if (key_led_light == KEY_LED_LEVEL_3)
    {
        led_blink_num_set_max = 3000;
        led_blink_num_set = 1500;
    }
    else if (key_led_light == KEY_LED_LEVEL_2)
    {
        led_blink_num_set_max = 2000;
        led_blink_num_set = 1000;
    }
    else if (key_led_light == KEY_LED_LEVEL_1)
    {
        led_blink_num_set_max = 1000;
        led_blink_num_set = 500;
    }

    switch (time_state)
    {
    case STATE_REAL_TIME:
        show_real_time();
        break;
    case STATE_SET_TIME_HOUR:
    case STATE_SET_TIME_MINUTE:
        set_real_time();
        break;
    case STATE_SET_ALARM_HOUR:
    case STATE_SET_ALARM_MINUTE:
        set_alarm_time();
        break;
    case STATE_COUNT_DOWN:
        show_timer_countdown();
        break;
    case STATE_SET_BT_NUM_THOUSAND:
    case STATE_SET_BT_NUM_HUNDRED:
    case STATE_SET_BT_NUM_TEN:
    case STATE_SET_BT_NUM_ONE:
        set_bt_num();
        break;
    case STATE_SET_COUNT_DOWN:
        set_timer_countdown();
        break;
    case STATE_BT_NUM:
        show_bt_num();
        break;
    case STATE_SNOOZE_CNT:
        show_snooze_cnt();
        break;
    case STATE_SET_SNOOZE_CNT:
        set_snooze_cnt();
        break;
    case STATE_POWER_OFF:
    case STATE_SLEEP:
        // 在睡眠状态下，如果有呼吸效果则显示睡眠前的状态内容，否则关闭显示
        if (display_led_breath_on_flag || display_led_breath_off_flag)
        {
            // 根据睡眠前的状态显示相应内容
            switch (time_state_before_sleep)
            {
            case STATE_REAL_TIME:
                show_real_time();
                break;
            case STATE_COUNT_DOWN:
                show_timer_countdown();
                break;
            case STATE_BT_NUM:
                show_bt_num();
                break;
            case STATE_SNOOZE_CNT:
                show_snooze_cnt();
                break;
            default:
                show_real_time(); // 默认显示实时时钟
                break;
            }
        }
        break;
    }
}
