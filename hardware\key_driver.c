#include "main.h"

uint8_t scan_key_enable = 0;

uint16_t key_iic_read(void)
{
    uint16_t key_data;
    iic_start();
    iic_write_byte(READ_ADDR);
    iic_recack();
    key_data = iic_read_byte() << 8;
    iic_ack();
    key_data |= iic_read_byte();
    iic_nack();
    iic_stop();
    return key_data;
}

void key_iic_driver(void)
{
    if (scan_key_enable)
    {

        key.curr_value = key_iic_read();
    }
    else
    {
        key.curr_value = 0;
    }

    if (key.curr_value != key.last_value)
    {
        key.last_value = key.curr_value;

        // 在播放音频时屏蔽所有按键音（包括闹钟、aux、睡眠音乐、蓝牙播放音乐）
        if (music_mode == MODE_BT || music_mode == MODE_AUX || music_mode == MODE_AMBIENT || alarm_state)
        {
            return;
        }

        // 只有在真正有按键中断信号时才发送按键提示音，防止误触发
        if (key.curr_value != 0 && KEY_INT)
        {
            key_data[0] = 0x01;
            key_data[1] = 0x20;
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;
        }

        return;
    }

    if (!key.lock)
    {
        if (key.curr_value)
        {
            if (key.state == KEY_RELEASE_DETECTED)
            {
                key.lock = 1;
                key.state = KEY_PRESS_DETECTED;
                key.key_do_value = key.curr_value;
                key.click_count++;
            }
            else if (key.state == KEY_MULTI_PRESS)
            {
                key.lock = 1;
                key.click_count++;
                key.interval_time = 0;
            }
        }
        else
        {
            if (key.state == KEY_MULTI_PRESS)
            {
                key.interval_time++;
                if (key.interval_time >= KEY_DOUBLE_TIME)
                {
                    key.interval_time = 0;
                    key.state = KEY_RELEASE_DETECTED;
                    if (key.click_count == 1)
                    {
                        key.type = KEY_TYPE_SHORT;
                        key.click_count = 0;
                    }
                    else if (key.click_count == 2)
                    {
                        key.type = KEY_TYPE_DOUBLE;
                        key.click_count = 0;
                    }
                    else
                    {
                        key.click_count = 0;
                    }
                }
            }
        }
    }
    else
    {
        if (key.curr_value) // 有按键值
        {
            if (key.state == KEY_PRESS_DETECTED)
            {
                if (key.curr_value == KEY_AMB || key.curr_value == KEY_AUX)
                {
                    key.long_time++;
                    if (key.long_time >= KEY_LONG_LONG_TIME)
                    {
                        key.long_time = 0;
                        key.click_count = 0;
                        key.state = KEY_LONG_PRESS;
                        key.type = KEY_TYPE_LONG;
                    }
                }
                else
                {
                    key.long_time++;
                    if (key.long_time >= KEY_LONG_TIME)
                    {
                        key.long_time = 0;
                        key.click_count = 0;
                        key.state = KEY_LONG_PRESS;
                        key.type = KEY_TYPE_LONG;
                    }
                }
            }
            else if (key.state == KEY_LONG_PRESS)
            {
                key.long_time++;
                if (key.long_time >= KEY_LONG_KEEP_TIME)
                {
                    key.long_time = 0;
                    key.type = KEY_TYPE_LONG_KEEP;
                }
            }
            else
            {
                key.long_time = 0;
            }
        }
        else // 无按键值
        {
            if (key.key_do_value == KEY_PLAY_PAUSE)
            {
                if (key.state == KEY_PRESS_DETECTED || key.state == KEY_MULTI_PRESS)
                {
                    key.lock = 0;
                    key.long_time = 0;
                    key.interval_time = 0;
                    key.state = KEY_MULTI_PRESS;
                }
            }
            else
            {
                key.long_time = 0;
                key.interval_time = 0;
                key.lock = 0;
                key.state = KEY_RELEASE_DETECTED;
                key.type = KEY_TYPE_SHORT;
                key.click_count = 0;
            }

            if (key.state == KEY_LONG_PRESS)
            {
                key.state = KEY_RELEASE_DETECTED;
                key.key_do_value = 0;
                key.lock = 0;
            }
        }
    }
}
