#ifndef __SWITCH_H__
#define __SWITCH_H__

#define IO_TIME_SET_KEY        P4_1
#define IO_ALARM_KEY           P5_4

#define SWITCH_LOCKED          1
#define SWITCH_UNLOCKED        0

#define SWITCH_TRGO_PRESS      1
#define SWITCH_TRGO_RELEASE    2
#define SWITCH_TRGO_LONG_PRESS 3

#define SWITCH_DEBOUNCE_TIME   4
#define SWITCH_LONGPRESS_TIME  200

typedef struct
{
    uint8_t lock;      // 锁定位
    uint8_t debounce;  // 消抖
    uint8_t value;     // 键值
    uint8_t longpress; // 长按
    uint8_t state;     // 状态
} switch_t;

extern uint8_t scan_switch_time;
extern uint8_t alarm_state;
extern uint8_t alarm_set_flag;
extern uint8_t snooze_state;
extern uint8_t already_countdown;
extern uint8_t last_snooze_time;
extern uint8_t alarm_auto_close_time;
extern uint8_t alarm_auto_close_flag;

void switch_hw_init();
void switch_state_init();
void switch_task();

#endif
