C51 COMPILER V9.60.7.0   UART                                                              08/01/2025 16:40:40 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE UART
OBJECT MODULE PLACED IN .\Objects\uart.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\hardware\uart.c LARGE OPTIMIZE(8,SPEED) BROWSE INCDIR(..\user;..\hardwar
                    -e) DEBUG OBJECTEXTEND PRINT(.\Listings\uart.lst) TABS(2) OBJECT(.\Objects\uart.obj)

line level    source

   1          #include "main.h"
   2          
   3          unsigned char uart0_recv_buf[5] = {0};
   4          unsigned char uart0_send_buf[5] = {0};
   5          unsigned char recv_count = 0;
   6          unsigned char recv_flag = 0;
   7          unsigned char packet_len = 5;
   8          
   9          uint8_t ble_pair_flag = 0;
  10          uint8_t ble_reconnect_flag = 0;
  11          
  12          void uart_init()
  13          {
  14   1          SET_REG(PORTIDX, PORT4);
  15   1          SET_REG_BITS(PINMOD54, PINMOD5, PIN_MODE_PP);
  16   1          SET_REG_BITS(PINMOD54, PINMOD4, PIN_MODE_OD_IPU);
  17   1          P4_4 = 1;
  18   1          P4_5 = 1;
  19   1      
  20   1          SET_REG_BITS(PINMODE, UART0PS, 2);    // UART0脚位选择P44 P45
  21   1          SET_REG_BITS(PCON, SMOD, SMOD_PRICE); // 双波特率控制位
  22   1      
  23   1          SM0 = 0;
  24   1          SM1 = 1; // 模式1
  25   1          SM2 = 0;
  26   1      
  27   1          SET_REG_BITS(UART0CON, UART0BRS, 1);
  28   1          SET_REG_BITS(UART0CON, UART0BRP, UART0BRP);
  29   1      
  30   1          TI = 0;  // 先清发送中断 标志
  31   1          RI = 0;  // 先清接收中断 标志
  32   1          REN = 1; // UART接收收据使能
  33   1          ES = 1;
  34   1      }
  35          
  36          void uart0_send(unsigned char *buf, unsigned char len)
  37          {
  38   1          unsigned char buffer[5] = {0};
  39   1          unsigned char check_sum = 0;
  40   1          unsigned char i = 0;
  41   1          // 帧头
  42   1          buffer[0] = 0x20;
  43   1          // 数据
  44   1          for (i = 0; i < len - 2; i++)
  45   1          {
  46   2              buffer[1 + i] = buf[i];
  47   2          }
  48   1          // 校验
  49   1          for (i = 0; i < len - 1; i++)
  50   1          {
  51   2              check_sum += buffer[i];
  52   2          }
  53   1          buffer[len - 1] = check_sum;
  54   1      
C51 COMPILER V9.60.7.0   UART                                                              08/01/2025 16:40:40 PAGE 2   

  55   1          for (i = 0; i < len; i++)
  56   1          {
  57   2              SBUF = buffer[i];
  58   2              while (TI == 0)
  59   2                  ;
  60   2              TI = 0;
  61   2          }
  62   1      }
  63          
  64          void uart_data_handle(void)
  65          {
  66   1          if (time_state == STATE_POWER_OFF)
  67   1              return;
  68   1      
  69   1          if (recv_flag)
  70   1          {
  71   2              recv_flag = 0;
  72   2              switch (uart0_recv_buf[1])
  73   2              {
  74   3              case 0x04:                         // 回连状态
  75   3                  if (uart0_recv_buf[2] == 0x01) // 蓝牙连接成功
  76   3                  {
  77   4                      ble_pair_flag = 0;
  78   4                      ble_reconnect_flag = 0;
  79   4                      music_mode = MODE_BT;
  80   4      
  81   4                      if (time_state == STATE_BT_NUM)
  82   4                      {
  83   5                          time_state = STATE_REAL_TIME;
  84   5                      }
  85   4      
  86   4                      // 发送蓝牙连接成功消息
  87   4                      uart0_send_buf[0] = 0x04;
  88   4                      uart0_send_buf[1] = 0x01;
  89   4                      uart0_send(uart0_send_buf, 4);
  90   4                      uart0_send_buf[0] = 0x00;
  91   4                      uart0_send_buf[1] = 0x00;
  92   4                  }
  93   3                  else if (uart0_recv_buf[2] == 0x02) // 蓝牙断开
  94   3                  {
  95   4                      if (music_mode == MODE_BT)
  96   4                      {
  97   5                          ble_pair_flag = 1;
  98   5                          ble_reconnect_flag = 0;
  99   5      
 100   5                          music_mode = MODE_BT_PAIR;
 101   5                          time_state = STATE_BT_NUM;
 102   5                          // 设置LED_WHITE呼吸效果的最大亮度为当前环境灯亮度
 103   5                          set_white_duty_config_by_ambient_light();
 104   5                      }
 105   4                  }
 106   3                  else if (uart0_recv_buf[2] == 0x04) // aux进入
 107   3                  {
 108   4                      music_mode = MODE_AUX;
 109   4                  }
 110   3                  else if (uart0_recv_buf[2] == 0x03) // aux进入失败
 111   3                  {
 112   4                      music_mode = MODE_IDE;
 113   4                  }
 114   3                  break;
 115   3              case 0x02: // 音量
 116   3                  if (music_mode == MODE_IDE)
C51 COMPILER V9.60.7.0   UART                                                              08/01/2025 16:40:40 PAGE 3   

 117   3                  {
 118   4                      vol.idl_vol = uart0_recv_buf[2];
 119   4                  }
 120   3                  else if (music_mode == MODE_BT)
 121   3                  {
 122   4                      vol.bt_vol = uart0_recv_buf[2];
 123   4                  }
 124   3                  else if (music_mode == MODE_AUX)
 125   3                  {
 126   4                      vol.aux_vol = uart0_recv_buf[2];
 127   4                  }
 128   3                  else if (music_mode == MODE_AMBIENT)
 129   3                  {
 130   4                      vol.ambient_vol = uart0_recv_buf[2];
 131   4                  }
 132   3                  break;
 133   3              case 0x03: // 时间
 134   3                  rtc.hour = uart0_recv_buf[2];
 135   3                  rtc.minute = uart0_recv_buf[3];
 136   3                  break;
 137   3              case 0x05: // 蓝牙名称
 138   3                  bt_name_num_thousand = uart0_recv_buf[2] / 10;
 139   3                  bt_name_num_hundred = uart0_recv_buf[2] % 10;
 140   3                  bt_name_num_ten = uart0_recv_buf[3] / 10;
 141   3                  bt_name_num_one = uart0_recv_buf[3] % 10;
 142   3                  break;
 143   3              }
 144   2      
 145   2              uart0_recv_buf[0] = 0;
 146   2              uart0_recv_buf[1] = 0;
 147   2              uart0_recv_buf[2] = 0;
 148   2              uart0_recv_buf[3] = 0;
 149   2              uart0_recv_buf[4] = 0;
 150   2          }
 151   1      }
 152          
 153          void uart0_irq() interrupt 4
 154          {
 155   1          unsigned char checksum = 0;
 156   1          unsigned char i = 0;
 157   1          if (RI)
 158   1          {
 159   2              uart0_recv_buf[recv_count] = SBUF; // 读取数据
 160   2              RI = 0;                            // 清除接收标志
 161   2      
 162   2              if (recv_count == 0)
 163   2              {
 164   3                  if (uart0_recv_buf[0] == 0x20) // 0x20为帧头
 165   3                  {
 166   4                      packet_len = 5;
 167   4                      recv_count++;
 168   4                  }
 169   3              }
 170   2              else if (recv_count == 1)
 171   2              {
 172   3                  if (uart0_recv_buf[1] == 0x02 || uart0_recv_buf[1] == 0x04)
 173   3                  {
 174   4                      packet_len = 4;
 175   4                  }
 176   3                  recv_count++;
 177   3              }
 178   2              else
C51 COMPILER V9.60.7.0   UART                                                              08/01/2025 16:40:40 PAGE 4   

 179   2              {
 180   3                  recv_count++;
 181   3              }
 182   2      
 183   2              if (recv_count >= packet_len)
 184   2              {
 185   3                  recv_count = 0;
 186   3                  for (i = 0; i < packet_len - 1; i++)
 187   3                  {
 188   4                      checksum += uart0_recv_buf[i];
 189   4                  }
 190   3      
 191   3                  if (checksum == uart0_recv_buf[packet_len - 1])
 192   3                  {
 193   4                      recv_flag = 1; // 接收成功
 194   4                  }
 195   3              }
 196   2          }
 197   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    740    ----
   CONSTANT SIZE    =      5    ----
   XDATA SIZE       =     15       9
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
