#include "main.h"

uint8_t scan_flash_time = 0;
uint8_t ble_blink_cnt = 0;
uint8_t timer_flash_cnt = 0; // 定时器闪灯

// 蓝牙配对闪烁
void ble_flash()
{
    if (ble_pair_flag)
    {
        ble_blink_cnt++;
        if (ble_blink_cnt >= 40)
        {
            ble_blink_cnt = 0;
        }
        if (ble_blink_cnt >= 20) // 200ms
        {
            KEY_LED_BT = 1;
        }
        else
        {
            KEY_LED_BT = 0;
        }
    }
    else
    {
        ble_blink_cnt = 0;
    }
}

// 设置定时器闪烁
void set_timer_flash()
{
    if (time_state == STATE_SET_COUNT_DOWN)
    {
        timer_flash_cnt++;
        if (timer_flash_cnt >= 40) // 400ms
        {
            timer_flash_cnt = 0;
        }
        if (timer_flash_cnt >= 20) // 200ms
        {
            KEY_LED_TIMER = 0;
        }
        else
        {
            KEY_LED_TIMER = 1;
        }
    }
    else
    {
        timer_flash_cnt = 0;
    }
}

void led_flash_task(void)
{
    if (time_state == STATE_POWER_OFF)
        return;
    if (scan_flash_time > 10)
    {
        scan_flash_time = 0;
#if BT_FLASH_ENABLE
        ble_flash();
#endif
        set_timer_flash();
    }
}