#ifndef __UART_H__
#define __UART_H__

// 波特率=(SMOD+1) x F SYSCLK /(32 x 2 x (256 – TH1))
#define SMOD_PRICE      1

#define BAUD4800        4800
#define BAUD9600        9600
#define BAUD19200       19200
#define BAUD38400       38400
#define BAUD57600       57600
#define BAUD115200      115200 // 必须在系统一分频下

#define UART0_BAUD_RATE BAUD115200

#define TIM_NUM         (unsigned int)(256 - ((((FRC_HZ / FRC_DIV) * (SMOD_PRICE + 1)) / 2) / 32 / UART0_BAUD_RATE)); // 计算不同波特率下的定时器值

#define UART0BRP        (unsigned int)(FRC_HZ / FRC_DIV / 32 / BAUD115200) // 计算不同波特率下的UART0BRP

extern uint8_t ble_pair_flag;
extern uint8_t ble_reconnect_flag;

void uart_init();
void uart0_send(unsigned char *buf, unsigned char len);
void uart_data_handle(void);

#endif
